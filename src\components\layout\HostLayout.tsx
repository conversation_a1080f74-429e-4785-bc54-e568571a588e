import { useEffect, useState } from "react";
import { useAuth } from "./AuthProvider";
import { useNavigate, Link, Outlet } from "react-router-dom";
import {
  Home,
  CalendarDays,
  DollarSign,
  MessageSquare,
  Settings,
} from "lucide-react";
import { Database } from "@/integrations/supabase/types";
import Navbar from "./Navbar";

type UserRole = Database["public"]["Enums"]["user_role"];

const HostLayout = () => {
  const { user, getUserRole } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isHost, setIsHost] = useState(false);

  useEffect(() => {
    const checkHostStatus = async () => {
      if (!user) {
        navigate("/auth");
        return;
      }

      try {
        const role = await getUserRole();

        if (role !== "host" && role !== "admin") {
          // Redirect non-hosts to the regular user profile
          navigate("/");
          return;
        }

        setIsHost(true);
      } catch (error) {
        console.error("Error checking host status:", error);
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    checkHostStatus();
  }, [user, navigate, getUserRole]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  if (!isHost) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Standard Navbar */}
      <Navbar />

      <div className="flex-1 flex flex-col container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row">
          <aside className="w-full md:w-64 md:mr-8 mb-6 md:mb-0">
            <nav className="bg-white p-4 rounded-lg shadow-sm">
              <ul className="space-y-2">
                <li>
                  <Link
                    to="/host"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <Home className="mr-3 h-5 w-5" />
                    <span>Dashboard</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/host/listings"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <Home className="mr-3 h-5 w-5" />
                    <span>Listings</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/host/reservations"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <CalendarDays className="mr-3 h-5 w-5" />
                    <span>Reservations</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/host/earnings"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <DollarSign className="mr-3 h-5 w-5" />
                    <span>Payments & Earnings</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/host/messages"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <MessageSquare className="mr-3 h-5 w-5" />
                    <span>Messages</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/host/settings"
                    className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
                  >
                    <Settings className="mr-3 h-5 w-5" />
                    <span>Settings</span>
                  </Link>
                </li>
              </ul>
            </nav>
          </aside>

          {/* Main Content */}
          <main className="flex-1 w-full">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};

export default HostLayout;
