
import { useState } from "react";
import { Button } from "@/components/ui/button";

// Mock data for explore categories
const exploreCategories = [
  {
    id: 1,
    name: "Beach Houses",
    image: "https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80",
  },
  {
    id: 2,
    name: "City Apartments",
    image: "https://images.unsplash.com/photo-1460317442991-0ec209397118?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
  },
  {
    id: 3,
    name: "Riverside Lodges",
    image: "https://images.unsplash.com/photo-1470770841072-f978cf4d019e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
  },
  {
    id: 4,
    name: "Cultural Stays",
    image: "https://images.unsplash.com/photo-1528277342758-f1d7613953a2?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
  },
];

// Mock data for popular destinations
const popularDestinations = [
  {
    id: 1,
    name: "Kololi",
    description: "Vibrant beach town with restaurants and nightlife",
    properties: 45,
    image: "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1173&q=80",
  },
  {
    id: 2,
    name: "Banjul",
    description: "The capital city with historical sites",
    properties: 32,
    image: "https://images.unsplash.com/photo-1477959858617-67f85cf4f1df?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1244&q=80",
  },
  {
    id: 3,
    name: "Bakau",
    description: "Known for its crocodile pool and botanical gardens",
    properties: 28,
    image: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
  },
];

const ExploreSection = () => {
  const [activeTab, setActiveTab] = useState("categories");

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-brown mb-10">
          Explore The Gambia
        </h2>

        {/* Tabs */}
        <div className="flex space-x-4 border-b border-gray-200 mb-8">
          <button
            className={`pb-4 px-2 font-medium ${
              activeTab === "categories"
                ? "text-secondary border-b-2 border-secondary"
                : "text-gray-500 hover:text-secondary"
            }`}
            onClick={() => setActiveTab("categories")}
          >
            Stay Categories
          </button>
          <button
            className={`pb-4 px-2 font-medium ${
              activeTab === "destinations"
                ? "text-secondary border-b-2 border-secondary"
                : "text-gray-500 hover:text-secondary"
            }`}
            onClick={() => setActiveTab("destinations")}
          >
            Popular Destinations
          </button>
        </div>

        {/* Categories Content */}
        {activeTab === "categories" && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {exploreCategories.map((category) => (
              <div
                key={category.id}
                className="relative rounded-lg overflow-hidden h-64 group cursor-pointer"
              >
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-black/20 flex items-end p-6">
                  <h3 className="text-white text-xl font-bold">{category.name}</h3>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Destinations Content */}
        {activeTab === "destinations" && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {popularDestinations.map((destination) => (
              <div
                key={destination.id}
                className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow"
              >
                <div className="h-48 overflow-hidden">
                  <img
                    src={destination.image}
                    alt={destination.name}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-brown mb-2">{destination.name}</h3>
                  <p className="text-gray-600 mb-4">{destination.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">
                      {destination.properties} properties
                    </span>
                    <Button variant="outline" className="text-secondary border-secondary hover:bg-secondary hover:text-white">
                      Explore
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default ExploreSection;
