import { useState } from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/layout/Navbar";
import SearchFilters from "@/components/properties/SearchFilters";
import PropertyCard from "@/components/properties/PropertyCard";
import { Plus } from "lucide-react";

const ListingsPage = () => {
  const { user } = useAuth();
  const [sortOption, setSortOption] = useState("recommended");

  const { data: properties, isLoading } = useQuery({
    queryKey: ["properties", sortOption],
    queryFn: async () => {
      let query = supabase.from("properties").select("*");

      if (sortOption === "price-low") {
        query = query.order("price", { ascending: true });
      } else if (sortOption === "price-high") {
        query = query.order("price", { ascending: false });
      }

      const { data, error } = await query;
      if (error) throw error;

      return data.map((property) => ({
        id: property.id,
        title: property.title,
        location: property.location,
        price: Number(property.price),
        rating: 4.7, // Temporary hardcoded rating
        reviews: 100, // Temporary hardcoded reviews
        image: property.images?.[0] || "/placeholder.svg",
        beds: Number(property.beds),
        baths: Number(property.baths),
        isSuperHost: false,
      }));
    },
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-3xl font-bold text-brown mb-4 md:mb-0">
            Find your perfect stay
          </h1>

          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <label className="mr-2 text-gray-600">Sort by:</label>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              >
                <option value="recommended">Recommended</option>
                <option value="price-low">Price (Low to High)</option>
                <option value="price-high">Price (High to Low)</option>
              </select>
            </div>

            {user && (
              <Link to="/listings/create">
                <Button className="bg-accent hover:bg-accent/90 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  List Property
                </Button>
              </Link>
            )}
          </div>
        </div>

        <SearchFilters />

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
          </div>
        ) : properties && properties.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {properties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              No properties found
            </h3>
            <p className="text-gray-500 mt-2">
              Try adjusting your filters or check back later.
            </p>
          </div>
        )}
      </main>

      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
            <p>© 2025 Gesco Stay. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ListingsPage;
