
import { useState } from 'react';

interface PaymentScheduleProps {
  defaultSchedule?: 'manual' | 'weekly' | 'monthly';
  onScheduleChange?: (schedule: 'manual' | 'weekly' | 'monthly') => void;
}

const PaymentSchedule = ({ 
  defaultSchedule = 'manual',
  onScheduleChange 
}: PaymentScheduleProps) => {
  const [schedule, setSchedule] = useState<'manual' | 'weekly' | 'monthly'>(defaultSchedule);

  const handleScheduleChange = (newSchedule: 'manual' | 'weekly' | 'monthly') => {
    setSchedule(newSchedule);
    if (onScheduleChange) {
      onScheduleChange(newSchedule);
    }
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <h3 className="font-medium mb-2">Payout Schedule</h3>
      <div className="space-y-2">
        <div className="flex items-center">
          <input 
            type="radio" 
            id="schedule-manual" 
            name="payout-schedule" 
            className="mr-2" 
            checked={schedule === 'manual'}
            onChange={() => handleScheduleChange('manual')}
          />
          <label htmlFor="schedule-manual">Manual Request (Default)</label>
        </div>
        <div className="flex items-center">
          <input 
            type="radio" 
            id="schedule-auto" 
            name="payout-schedule" 
            className="mr-2"
            checked={schedule === 'weekly'}
            onChange={() => handleScheduleChange('weekly')}
          />
          <label htmlFor="schedule-auto">Automatic (Weekly)</label>
        </div>
        <div className="flex items-center">
          <input 
            type="radio" 
            id="schedule-monthly" 
            name="payout-schedule" 
            className="mr-2"
            checked={schedule === 'monthly'}
            onChange={() => handleScheduleChange('monthly')}
          />
          <label htmlFor="schedule-monthly">Monthly</label>
        </div>
      </div>
    </div>
  );
};

export default PaymentSchedule;
