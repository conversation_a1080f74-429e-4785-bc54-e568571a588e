
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useCarListings } from "@/hooks/useCarListings";
import CarCard from "@/components/cars/CarCard";

const CarRentalSection = () => {
  const { data: cars = [], isLoading } = useCarListings();
  
  // Display only the first 4 cars
  const featuredCars = cars.slice(0, 4);
  
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Explore Car Rentals</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            From compact cars to luxury SUVs, find the perfect vehicle for your journey through The Gambia.
          </p>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
          </div>
        ) : featuredCars.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredCars.map(car => (
                <CarCard key={car.id} car={car} />
              ))}
            </div>
            
            <div className="text-center mt-12">
              <Button asChild size="lg">
                <Link to="/cars">View All Car Rentals</Link>
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-10">
            <p className="text-lg text-gray-600 mb-6">Ready to rent your car?</p>
            <Button asChild size="lg">
              <Link to="/cars/create">List Your Car</Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default CarRentalSection;
