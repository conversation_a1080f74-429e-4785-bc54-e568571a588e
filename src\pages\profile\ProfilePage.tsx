import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/layout/Navbar";
import { Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface UserPreferences {
  id?: string;
  user_id: string;
  booking_confirmations: boolean;
  status_updates: boolean;
  reminders: boolean;
  created_at?: string;
  updated_at?: string;
}

const ProfilePage = () => {
  const { user, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [editableProfile, setEditableProfile] = useState({
    first_name: "",
    last_name: "",
  });
  const [emailPreferences, setEmailPreferences] = useState<UserPreferences>({
    user_id: user?.id || "",
    booking_confirmations: true,
    status_updates: true,
    reminders: true,
  });

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user) {
        navigate("/auth");
        return;
      }

      try {
        setLoading(true);

        // Fetch profile data
        const { data, error } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        if (error) throw error;

        setProfile(data);
        setEditableProfile({
          first_name: data?.first_name || "",
          last_name: data?.last_name || "",
        });

        // Use a direct REST call to the RPC function to avoid TypeScript issues
        const response = await fetch(
          `https://meakrzwthtkkumudxhzv.supabase.co/rest/v1/rpc/get_user_preferences`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              apikey:
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYWtyend0aHRra3VtdWR4aHp2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNTYyNjAsImV4cCI6MjA2MDgzMjI2MH0.Xe16gV1UA_5yeh7WfKzI9wRdAnYuwyszhMpRlq3GHEw",
              Authorization: `Bearer ${supabase.auth
                .getSession()
                .then((res) => res.data.session?.access_token)}`,
            },
            body: JSON.stringify({ user_id_input: user.id }),
          }
        );

        if (response.ok) {
          const prefsData = await response.json();
          setEmailPreferences({
            user_id: user.id,
            booking_confirmations: prefsData.booking_confirmations || true,
            status_updates: prefsData.status_updates || true,
            reminders: prefsData.reminders || true,
          });
        }
      } catch (error: any) {
        console.error("Error fetching profile:", error);
        toast({
          title: "Error",
          description: "Failed to load your profile data.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [user, navigate, toast]);

  const updateEmailPreferences = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Use a direct REST call to the RPC function to avoid TypeScript issues
      const response = await fetch(
        `https://meakrzwthtkkumudxhzv.supabase.co/rest/v1/rpc/update_user_preferences`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            apikey:
              "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYWtyend0aHRra3VtdWR4aHp2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNTYyNjAsImV4cCI6MjA2MDgzMjI2MH0.Xe16gV1UA_5yeh7WfKzI9wRdAnYuwyszhMpRlq3GHEw",
            Authorization: `Bearer ${await supabase.auth
              .getSession()
              .then((res) => res.data.session?.access_token)}`,
          },
          body: JSON.stringify({
            user_id_input: user.id,
            booking_confirmations_input: emailPreferences.booking_confirmations,
            status_updates_input: emailPreferences.status_updates,
            reminders_input: emailPreferences.reminders,
          }),
        }
      );

      if (response.ok) {
        toast({
          title: "Preferences Updated",
          description: "Your email notification preferences have been saved.",
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update preferences");
      }
    } catch (error: any) {
      console.error("Error updating preferences:", error);
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateAccountDetails = async () => {
    if (!user) return;

    try {
      setSaving(true);

      // Update profile in Supabase
      await updateProfile({
        firstName: editableProfile.first_name,
        lastName: editableProfile.last_name,
      });

      // Update auth metadata
      const { error: authError } = await supabase.auth.updateUser({
        data: {
          first_name: editableProfile.first_name,
          last_name: editableProfile.last_name,
        },
      });

      if (authError) throw authError;

      // Update local state
      setProfile({
        ...profile,
        first_name: editableProfile.first_name,
        last_name: editableProfile.last_name,
      });

      toast({
        title: "Profile Updated",
        description: "Your account details have been successfully updated.",
      });
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update your profile.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 pt-8 flex items-center justify-center min-h-[calc(100vh-80px)]">
          <Loader2 className="h-8 w-8 animate-spin text-accent" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-start gap-6 mb-8">
            <div className="w-full md:w-1/3">
              <Card>
                <CardHeader>
                  <div className="flex flex-col items-center">
                    <Avatar className="h-24 w-24 mb-4">
                      <AvatarImage
                        src={profile?.avatar_url || ""}
                        alt={profile?.first_name || "User"}
                      />
                      <AvatarFallback>
                        {profile?.first_name?.[0] ||
                          user?.email?.[0]?.toUpperCase() ||
                          "U"}
                      </AvatarFallback>
                    </Avatar>
                    <CardTitle>
                      {editableProfile.first_name || profile?.first_name}{" "}
                      {editableProfile.last_name || profile?.last_name}
                    </CardTitle>
                    <CardDescription>{user?.email}</CardDescription>
                  </div>
                </CardHeader>
              </Card>
            </div>
            <div className="w-full md:w-2/3">
              <Tabs defaultValue="preferences">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="preferences">
                    Email Preferences
                  </TabsTrigger>
                  <TabsTrigger value="account">Account Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="preferences" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Email Notifications</CardTitle>
                      <CardDescription>
                        Manage when you'll receive emails from us
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium">Booking Confirmations</h3>
                          <p className="text-sm text-gray-500">
                            Receive emails when your bookings are confirmed
                          </p>
                        </div>
                        <Switch
                          checked={emailPreferences.booking_confirmations}
                          onCheckedChange={(checked) =>
                            setEmailPreferences({
                              ...emailPreferences,
                              booking_confirmations: checked,
                            })
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium">Status Updates</h3>
                          <p className="text-sm text-gray-500">
                            Receive emails when your booking status changes
                          </p>
                        </div>
                        <Switch
                          checked={emailPreferences.status_updates}
                          onCheckedChange={(checked) =>
                            setEmailPreferences({
                              ...emailPreferences,
                              status_updates: checked,
                            })
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium">Reminders</h3>
                          <p className="text-sm text-gray-500">
                            Receive reminders about upcoming bookings
                          </p>
                        </div>
                        <Switch
                          checked={emailPreferences.reminders}
                          onCheckedChange={(checked) =>
                            setEmailPreferences({
                              ...emailPreferences,
                              reminders: checked,
                            })
                          }
                        />
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button onClick={updateEmailPreferences}>
                        Save Preferences
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>

                <TabsContent value="account" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Account Management</CardTitle>
                      <CardDescription>
                        Update your account details
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="first_name">First Name</Label>
                        <Input
                          id="first_name"
                          value={editableProfile.first_name}
                          onChange={(e) =>
                            setEditableProfile({
                              ...editableProfile,
                              first_name: e.target.value,
                            })
                          }
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="last_name">Last Name</Label>
                        <Input
                          id="last_name"
                          value={editableProfile.last_name}
                          onChange={(e) =>
                            setEditableProfile({
                              ...editableProfile,
                              last_name: e.target.value,
                            })
                          }
                          placeholder="Enter your last name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={user?.email || ""}
                          disabled
                          className="bg-gray-50"
                        />
                        <p className="text-sm text-gray-500">
                          Email cannot be changed. Contact support if needed.
                        </p>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button
                        onClick={updateAccountDetails}
                        disabled={saving}
                        className="w-full"
                      >
                        {saving ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Updating...
                          </>
                        ) : (
                          "Update Account Details"
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-2xl font-bold mb-4">Your Activity</h2>
            <Card>
              <CardHeader>
                <CardTitle>Recent Bookings</CardTitle>
              </CardHeader>
              <CardContent>
                <Button onClick={() => navigate("/bookings")} variant="outline">
                  View All Bookings
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
