
-- Deploy all custom functions
\i 'supabase/migrations/20250521_payout_requests_function.sql'
\i 'supabase/migrations/20250521_host_payout_requests_function.sql'

-- Calculate platform fee (10%)
CREATE OR REPLACE FUNCTION public.calculate_platform_fee(
  booking_amount numeric,
  booking_type text DEFAULT 'property'
) 
RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  platform_fee NUMERIC;
BEGIN
  -- Calculate 10% platform fee
  platform_fee := booking_amount * 0.10;
  RETURN platform_fee;
END;
$$;
