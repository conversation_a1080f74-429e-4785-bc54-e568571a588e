import Hero from "@/components/home/<USER>";
import FeaturedListings from "@/components/home/<USER>";
import ExploreSection from "@/components/home/<USER>";
import CarRentalSection from "@/components/home/<USER>";
import AboutSection from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";

const Index = () => {
  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <Hero />
      <AboutSection />
      <FeaturedListings />
      <CarRentalSection />
      <ExploreSection />

      {/* Call to Action Section */}
      <section className="py-16 bg-secondary/10">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-brown mb-6">
              Join the Gescostay Community
            </h2>
            <p className="text-gray-600 mb-8 text-lg">
              Whether you're hosting your property or listing your car, become
              part of Africa's most trusted travel platform and start earning
              with confidence.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                className="bg-accent hover:bg-accent/90 text-white px-8 py-6 text-lg"
                asChild
              >
                <Link to="/listings/create">Start Hosting</Link>
              </Button>
              <Button
                className="bg-secondary hover:bg-secondary/90 text-white px-8 py-6 text-lg"
                asChild
              >
                <Link to="/cars/create">List Your Car</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Admin Access Section */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-lg mx-auto text-center">
            <h3 className="text-xl font-semibold mb-2">
              Platform Administration
            </h3>
            <p className="text-gray-600 mb-4">
              Access the admin dashboard to manage properties, vehicles, users,
              and bookings.
            </p>
            <Button variant="outline" asChild>
              <Link to="/admin/auth">Admin Login</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer Section */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Gesco Stay</h3>
              <p className="text-gray-300">
                Discover unique stays and car rentals across The Gambia - the
                smiling coast of Africa.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-4">Navigation</h4>
              <ul className="space-y-2">
                <li>
                  <Link to="/" className="text-gray-300 hover:text-white">
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    to="/listings"
                    className="text-gray-300 hover:text-white"
                  >
                    Properties
                  </Link>
                </li>
                <li>
                  <Link to="/cars" className="text-gray-300 hover:text-white">
                    Car Rentals
                  </Link>
                </li>
                <li>
                  <Link to="#" className="text-gray-300 hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="#" className="text-gray-300 hover:text-white">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-4">Host</h4>
              <ul className="space-y-2">
                <li>
                  <Link
                    to="/listings/create"
                    className="text-gray-300 hover:text-white"
                  >
                    List Property
                  </Link>
                </li>
                <li>
                  <Link
                    to="/cars/create"
                    className="text-gray-300 hover:text-white"
                  >
                    List Car
                  </Link>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Resources
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Community
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-4">Support</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Safety
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
            <p>© 2025 Gesco Stay. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
