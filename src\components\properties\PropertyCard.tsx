
import { Link } from "react-router-dom";
import { MapPin, Star, Bed, Bath } from "lucide-react";

interface PropertyCardProps {
  property: {
    id: string;
    title: string;
    location: string;
    price: number;
    rating: number;
    reviews: number;
    image: string;
    beds: number;
    baths: number;
    isSuperHost?: boolean;
  };
}

const PropertyCard = ({ property }: PropertyCardProps) => {
  const {
    id,
    title,
    location,
    price,
    rating,
    reviews,
    image,
    beds,
    baths,
    isSuperHost = false,
  } = property;

  return (
    <Link to={`/listings/${id}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-shadow hover:shadow-md">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden">
          <img
            src={image || "/placeholder.svg"}
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          {isSuperHost && (
            <div className="absolute top-2 left-2 px-2 py-1 bg-white text-xs font-medium rounded-full shadow-sm">
              Superhost
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-gray-900 truncate" title={title}>
              {title}
            </h3>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="ml-1 text-sm text-gray-700">{rating}</span>
            </div>
          </div>

          <div className="flex items-center text-sm text-gray-500 mb-2">
            <MapPin className="h-3 w-3 mr-1" />
            <span className="truncate" title={location}>
              {location}
            </span>
          </div>

          <div className="flex items-center text-sm text-gray-500 mb-3">
            <Bed className="h-3 w-3 mr-1" />
            <span>{beds} {beds === 1 ? 'bed' : 'beds'}</span>
            <span className="mx-1">·</span>
            <Bath className="h-3 w-3 mr-1" />
            <span>{baths} {baths === 1 ? 'bath' : 'baths'}</span>
          </div>

          <div className="font-medium">
            <span className="text-gray-900">${price}</span>
            <span className="text-gray-500"> / night</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PropertyCard;
