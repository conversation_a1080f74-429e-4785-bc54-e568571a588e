import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useAuth } from "./AuthProvider";
import { Menu, X } from "lucide-react";
import NavbarUserMenu from "./NavbarUserMenu";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center">
            <img
              src="/images/logo.jpg"
              alt="Gesco Stay"
              className="h-10 w-auto mr-2"
            />
            <span className="text-2xl font-bold text-gray-800">Gesco Stay</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link to="/listings" className="text-gray-600 hover:text-gray-900">
              Properties
            </Link>
            <Link to="/cars" className="text-gray-600 hover:text-gray-900">
              Car Rentals
            </Link>
            {user && (
              <>
                <Link
                  to="/listings/create"
                  className="text-gray-600 hover:text-gray-900"
                >
                  List Property
                </Link>
                <Link
                  to="/cars/create"
                  className="text-gray-600 hover:text-gray-900"
                >
                  List Car
                </Link>
              </>
            )}
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <NavbarUserMenu />
          </div>

          {/* Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="sm">
                {isOpen ? <X /> : <Menu />}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[250px] sm:w-[300px]">
              <nav className="flex flex-col gap-4 mt-8">
                <SheetClose asChild>
                  <Link
                    to="/"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Home
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    to="/listings"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Properties
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    to="/cars"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Car Rentals
                  </Link>
                </SheetClose>
                {user && (
                  <>
                    <SheetClose asChild>
                      <Link
                        to="/listings/create"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        List Property
                      </Link>
                    </SheetClose>
                    <SheetClose asChild>
                      <Link
                        to="/cars/create"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        List Car
                      </Link>
                    </SheetClose>
                  </>
                )}
                <div className="border-t my-4"></div>
                <div className="px-4">
                  <NavbarUserMenu />
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
