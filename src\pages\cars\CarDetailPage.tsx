import { useState } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import { useCarDetails } from "@/hooks/useCarDetails";
import CarGallery from "@/components/cars/CarGallery";
import CarDetailHeader from "@/components/cars/CarDetailHeader";
import CarBookingWidget from "@/components/cars/CarBookingWidget";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

const CarDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const { data: car, isLoading, error } = useCarDetails(id);
  const [activeTab, setActiveTab] = useState("details");

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
        </div>
      </div>
    );
  }

  if (error || !car) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-10">
            <h3 className="text-lg font-semibold text-red-600">
              Error loading car details
            </h3>
            <p className="text-muted-foreground">
              {(error as Error).message || "Car not found"}
            </p>
            <Button variant="outline" asChild className="mt-4">
              <Link to="/cars">Back to Car Listings</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/cars" className="flex items-center">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Car Listings
          </Link>
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-8">
            <CarGallery
              images={car.images.length ? car.images : ["/placeholder.svg"]}
              title={car.title}
            />

            <CarDetailHeader car={car} />

            <Separator />

            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 w-full">
                <TabsTrigger value="details">Details & Features</TabsTrigger>
                <TabsTrigger value="policies">Rental Policies</TabsTrigger>
              </TabsList>
              <TabsContent value="details" className="pt-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">
                      Car Specifications
                    </h3>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Make:</span>
                        <span>{car.make}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Model:</span>
                        <span>{car.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Year:</span>
                        <span>{car.year}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Type:</span>
                        <span className="capitalize">{car.car_type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Seats:</span>
                        <span>{car.seats}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Transmission:
                        </span>
                        <span className="capitalize">{car.transmission}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Fuel Type:
                        </span>
                        <span className="capitalize">{car.fuel_type}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold">Features</h3>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {car.features && car.features.length > 0 ? (
                        car.features.map((feature, index) => (
                          <div key={index} className="flex items-center">
                            <span>✓</span>
                            <span className="ml-2">{feature}</span>
                          </div>
                        ))
                      ) : (
                        <p className="text-muted-foreground">
                          No features listed
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="policies" className="pt-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">Rental Policies</h3>
                    <div className="space-y-3 mt-2">
                      <div>
                        <h4 className="font-medium">Minimum Age</h4>
                        <p className="text-muted-foreground">
                          Driver must be at least 21 years old
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Required Documents</h4>
                        <p className="text-muted-foreground">
                          Valid driver's license, passport or ID, and credit
                          card
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Security Deposit</h4>
                        <p className="text-muted-foreground">
                          A security deposit may be required at pick-up
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Fuel Policy</h4>
                        <p className="text-muted-foreground">
                          Full to full. Car is provided with a full tank and
                          should be returned full.
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Mileage Policy</h4>
                        <p className="text-muted-foreground">
                          Unlimited mileage included
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Cancellation</h4>
                        <p className="text-muted-foreground">
                          Free cancellation up to 24 hours before pick-up
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div>
            <CarBookingWidget
              carId={car.id}
              carTitle={car.title}
              priceDay={car.price_day}
              priceWeek={car.price_week}
              priceMonth={car.price_month}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="container mx-auto px-4">
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
            <p>© 2025 Gesco Stay. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CarDetailPage;
