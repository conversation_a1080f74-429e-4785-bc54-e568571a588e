
import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Line,
  Pie,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

type ChartProps = {
  data: any;
  width?: number;
  height?: number;
  options?: any;
};

export const BarChart = ({ data, width = 500, height = 300, options }: ChartProps) => {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsBarChart data={data.labels.map((label: string, index: number) => ({
        name: label,
        ...data.datasets.reduce((acc: any, dataset: any, i: number) => {
          acc[dataset.label || `dataset-${i}`] = dataset.data[index];
          return acc;
        }, {})
      }))}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        {data.datasets.map((dataset: any, index: number) => (
          <Bar 
            key={index} 
            dataKey={dataset.label || `dataset-${index}`} 
            fill={dataset.backgroundColor || `#${Math.floor(Math.random()*16777215).toString(16)}`} 
          />
        ))}
      </RechartsBarChart>
    </ResponsiveContainer>
  );
};

export const LineChart = ({ data, width = 500, height = 300, options }: ChartProps) => {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsLineChart data={data.labels.map((label: string, index: number) => ({
        name: label,
        ...data.datasets.reduce((acc: any, dataset: any, i: number) => {
          acc[dataset.label || `dataset-${i}`] = dataset.data[index];
          return acc;
        }, {})
      }))}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        {data.datasets.map((dataset: any, index: number) => (
          <Line
            key={index}
            type="monotone"
            dataKey={dataset.label || `dataset-${index}`}
            stroke={dataset.borderColor || `#${Math.floor(Math.random()*16777215).toString(16)}`}
            activeDot={{ r: 8 }}
          />
        ))}
      </RechartsLineChart>
    </ResponsiveContainer>
  );
};

export const PieChart = ({ data, width = 500, height = 300, options }: ChartProps) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  
  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsPieChart>
        <Tooltip />
        <Legend />
        {data.datasets.map((dataset: any, datasetIndex: number) => (
          <Pie
            key={datasetIndex}
            data={data.labels.map((label: string, index: number) => ({
              name: label,
              value: dataset.data[index]
            }))}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.labels.map((entry: any, index: number) => (
              <Cell key={`cell-${index}`} fill={dataset.backgroundColor?.[index] || COLORS[index % COLORS.length]} />
            ))}
          </Pie>
        ))}
      </RechartsPieChart>
    </ResponsiveContainer>
  );
};
