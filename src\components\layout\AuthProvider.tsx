import { createContext, useContext, useEffect, useState } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";

type UserRole = Database["public"]["Enums"]["user_role"];

interface AuthContextType {
  session: Session | null;
  user: User | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (
    email: string,
    password: string,
    firstName: string,
    lastName: string
  ) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: {
    firstName?: string;
    lastName?: string;
    role?: UserRole;
  }) => Promise<void>;
  getUserRole: () => Promise<UserRole | null>;
  getUserRoles: () => Promise<string[]>;
  hasRole: (role: string) => Promise<boolean>;
  loading: boolean;
  switchRole: (newRole: UserRole) => Promise<void>;
  becomeHost: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  resetPassword: async () => {},
  updateProfile: async () => {},
  getUserRole: async () => null,
  getUserRoles: async () => [],
  hasRole: async () => false,
  loading: true,
  switchRole: async () => {},
  becomeHost: async () => {},
});

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // Provide more specific error messages
      if (error.message.includes("Invalid login credentials")) {
        throw new Error(
          "Invalid email or password. Please check your credentials and try again."
        );
      } else if (error.message.includes("Email not confirmed")) {
        throw new Error(
          "Please check your email and click the confirmation link before signing in."
        );
      } else if (error.message.includes("Too many requests")) {
        throw new Error(
          "Too many login attempts. Please wait a moment before trying again."
        );
      } else {
        throw new Error(error.message || "An error occurred during sign in.");
      }
    }

    // After sign in, check user role to redirect accordingly
    const userRole = await getUserRole();
    if (userRole === "host") {
      navigate("/host");
    } else if (userRole === "admin") {
      navigate("/admin/dashboard");
    } else {
      navigate("/");
    }
  };

  const signUp = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string
  ) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
        },
      },
    });

    if (error) {
      // Provide more specific error messages
      if (error.message.includes("User already registered")) {
        throw new Error(
          "An account with this email already exists. Please sign in instead."
        );
      } else if (error.message.includes("Password should be at least")) {
        throw new Error("Password should be at least 6 characters long.");
      } else if (error.message.includes("Unable to validate email address")) {
        throw new Error("Please enter a valid email address.");
      } else if (error.message.includes("Signup is disabled")) {
        throw new Error(
          "Account registration is currently disabled. Please contact support."
        );
      } else {
        throw new Error(error.message || "An error occurred during sign up.");
      }
    }
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    navigate("/");
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    if (error) throw error;
  };

  const updateProfile = async (data: {
    firstName?: string;
    lastName?: string;
    role?: UserRole;
  }) => {
    if (!user) throw new Error("No user logged in");

    try {
      const updates = {
        id: user.id,
        first_name: data.firstName,
        last_name: data.lastName,
        role: data.role,
        updated_at: new Date().toISOString(),
      };

      // Filter out undefined values
      Object.keys(updates).forEach((key) => {
        if (updates[key as keyof typeof updates] === undefined) {
          delete updates[key as keyof typeof updates];
        }
      });

      // Use the session for authentication
      const { error } = await supabase
        .from("profiles")
        .upsert(updates)
        .select();

      if (error) throw error;
    } catch (error) {
      console.error("Error updating profile:", error);
      throw error;
    }
  };

  const getUserRole = async (): Promise<UserRole | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("role, roles")
        .eq("id", user.id)
        .single();

      if (error) throw error;

      return data.role;
    } catch (error) {
      console.error("Error fetching user role:", error);
      return null;
    }
  };

  const getUserRoles = async (): Promise<string[]> => {
    if (!user) return [];

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("roles")
        .eq("id", user.id)
        .single();

      if (error) {
        console.error("Error fetching user roles:", error);
        throw error;
      }

      const roles = data?.roles || ["guest"];
      return roles;
    } catch (error) {
      console.error("Error fetching user roles:", error);
      return ["guest"];
    }
  };

  const hasRole = async (role: string): Promise<boolean> => {
    const roles = await getUserRoles();
    return roles.includes(role);
  };

  const switchRole = async (newRole: UserRole) => {
    if (!user) throw new Error("No user logged in");

    try {
      // Update the role
      const { error } = await supabase
        .from("profiles")
        .update({
          role: newRole,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      if (error) {
        console.error("Error switching role:", error);
        throw error;
      }

      // Add a small delay to ensure database is updated before navigation
      setTimeout(() => {
        // Redirect based on the new role
        if (newRole === "host") {
          navigate("/host");
        } else if (newRole === "guest") {
          navigate("/");
        }
      }, 500);
    } catch (error) {
      console.error("Error switching role:", error);
      throw error;
    }
  };

  const becomeHost = async () => {
    if (!user) throw new Error("No user logged in");

    try {
      // Get current roles
      const currentRoles = await getUserRoles();

      // Add host role if not already present
      if (!currentRoles.includes("host")) {
        const newRoles = [...currentRoles, "host"];

        const { error } = await supabase
          .from("profiles")
          .update({
            role: "host", // Keep the primary role as host for compatibility
            roles: newRoles,
            updated_at: new Date().toISOString(),
          })
          .eq("id", user.id);

        if (error) {
          console.error("Error becoming host:", error);
          throw error;
        }

        toast.success("You are now a host!");
      }

      // Navigate to host dashboard
      setTimeout(() => {
        navigate("/host");
      }, 500);
    } catch (error) {
      console.error("Error becoming host:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      toast.error(`Failed to become host: ${errorMessage}`);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        signIn,
        signUp,
        signOut,
        resetPassword,
        updateProfile,
        getUserRole,
        getUserRoles,
        hasRole,
        loading,
        switchRole,
        becomeHost,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
