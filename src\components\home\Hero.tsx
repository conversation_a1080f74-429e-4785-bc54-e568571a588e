import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

const Hero = () => {
  return (
    <div className="relative h-[600px] md:h-[700px] overflow-hidden">
      {/* Hero Background - Ocean */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1505142468610-359e7d316be0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
        }}
      >
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black opacity-40"></div>
      </div>

      {/* Hero Content */}
      <div className="relative container mx-auto h-full flex flex-col items-center justify-center px-4 text-center">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          Welcome to Gescostay — Africa's Gateway to Trustworthy Travel & Stays
        </h1>
        <p className="text-xl md:text-2xl text-white mb-4 max-w-4xl">
          Discover comfort, connect with culture, and book with confidence.
        </p>
        <p className="text-lg md:text-xl text-white mb-8 max-w-4xl">
          Gescostay is Africa's next-generation booking platform, where verified
          homes, apartments, and rentals meet seamless technology — designed for
          locals, the diaspora, and global travelers.
        </p>

        <div className="flex flex-wrap justify-center items-center gap-6 mb-8 text-white">
          <div className="flex items-center gap-2">
            <span className="text-2xl">🛏</span>
            <span className="font-semibold">Book. Stay. Belong.</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-2xl">✨</span>
            <span className="font-semibold">100% verified hosts</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-2xl">🇬🇲</span>
            <span className="font-semibold">
              Book Local. Live Global. Stay Connected.
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-2xl">🔒</span>
            <span className="font-semibold">Secure payments</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-2xl">🤝</span>
            <span className="font-semibold">Trusted by communities</span>
          </div>
        </div>

        {/* Search form */}
        <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg p-4 md:p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-brown mb-1">
                Where
              </label>
              <input
                type="text"
                placeholder="Destination"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-brown mb-1">
                Check-in
              </label>
              <input
                type="date"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-brown mb-1">
                Check-out
              </label>
              <input
                type="date"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              />
            </div>
          </div>

          <div className="mt-4 flex justify-center">
            <Button className="bg-accent hover:bg-accent/90 text-white px-8 py-2 flex items-center gap-2">
              <Search size={18} />
              <span>Search</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
