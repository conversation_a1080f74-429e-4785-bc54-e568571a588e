import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Star, CreditCard, Wallet } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { usePropertyBookedDates } from "@/hooks/useBookedDates";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface PropertyBookingWidgetProps {
  price: number;
  rating: number;
  propertyId: string;
}

const PropertyBookingWidget = ({
  price,
  rating,
  propertyId,
}: PropertyBookingWidgetProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [isBookingMode, setIsBookingMode] = useState(false);
  const [checkInDate, setCheckInDate] = useState<Date | undefined>(undefined);
  const [checkOutDate, setCheckOutDate] = useState<Date | undefined>(undefined);
  const [guests, setGuests] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [totalAmount, setTotalAmount] = useState(0);

  // Fetch booked dates for this property
  const { data: bookedDates = [] } = usePropertyBookedDates(propertyId);

  const handleBooking = async () => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to book this property",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    if (!checkInDate || !checkOutDate) {
      toast({
        title: "Please select check-in and check-out dates",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Calculate number of days
      const days = Math.ceil(
        (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Calculate total price (base price * days)
      const amount = price * days;
      setTotalAmount(amount);

      // Check property availability
      const { data: isAvailable } = await supabase.rpc(
        "check_property_availability",
        {
          property_id: propertyId,
          check_in_date: checkInDate.toISOString().split("T")[0],
          check_out_date: checkOutDate.toISOString().split("T")[0],
        }
      );

      if (!isAvailable) {
        toast({
          title: "Property not available",
          description: "This property is not available for the selected dates",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Create booking record
      const { data: booking, error } = await supabase
        .from("bookings")
        .insert({
          property_id: propertyId,
          user_id: user.id,
          check_in: checkInDate.toISOString().split("T")[0],
          check_out: checkOutDate.toISOString().split("T")[0],
          total_price: amount,
          status: "pending",
        })
        .select("id")
        .single();

      if (error) throw error;

      setBookingId(booking.id);
      setShowPaymentDialog(true);
    } catch (error: any) {
      console.error("Booking error:", error);
      toast({
        title: "Booking failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStripePayment = async () => {
    if (!bookingId || !user?.email) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke(
        "process-stripe-payment",
        {
          body: {
            bookingId,
            propertyId,
            amount: totalAmount,
            returnUrl: window.location.origin,
            userEmail: user.email,
          },
        }
      );

      if (error) throw error;

      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      } else {
        throw new Error("No session URL returned");
      }
    } catch (error: any) {
      console.error("Payment error:", error);
      toast({
        title: "Payment failed",
        description: error.message,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const handleWavePayment = async () => {
    if (!bookingId || !user?.email) return;

    setIsLoading(true);
    try {
      // Use supabase.functions.invoke to call the process-wave-payment edge function (cloud)
      const { data: result, error } = await supabase.functions.invoke(
        "process-wave-payment",
        {
          body: {
            bookingId,
            bookingType: "property",
            amount: totalAmount,
            returnUrl: window.location.origin,
            userEmail: user.email,
            userMobile: user.phone || "",
          },
        }
      );

      if (error) {
        toast({
          title: "Wave payment failed",
          description: error.message || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      } else if (result?.success && result?.checkoutUrl) {
        window.location.href = result.checkoutUrl;
      } else {
        toast({
          title: "Wave payment failed",
          description: result?.error || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      const err = error as Error;
      console.error("Wave payment error:", err);
      toast({
        title: "Payment failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotalPrice = () => {
    if (!checkInDate || !checkOutDate) return 0;

    const days = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    return price * days;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
      <div className="flex items-center justify-between mb-4">
        <div>
          <span className="text-2xl font-bold">${price}</span>
          <span className="text-gray-600"> night</span>
        </div>
        <div className="flex items-center">
          <Star className="w-4 h-4 text-yellow-500 mr-1" />
          <span>{rating}</span>
        </div>
      </div>

      {isBookingMode ? (
        <div>
          <div className="border border-gray-200 rounded-lg p-4 mb-4">
            <div className="mb-4">
              <DateRangePicker
                checkInDate={checkInDate}
                checkOutDate={checkOutDate}
                onCheckInChange={setCheckInDate}
                onCheckOutChange={setCheckOutDate}
                disabledDates={bookedDates}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guests
              </label>
              <select
                value={guests}
                onChange={(e) => setGuests(Number(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              >
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <option key={num} value={num}>
                    {num} {num === 1 ? "guest" : "guests"}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Button
            onClick={handleBooking}
            className="w-full bg-secondary hover:bg-secondary/90 text-white mb-4"
            disabled={!checkInDate || !checkOutDate || isLoading}
          >
            {isLoading ? "Processing..." : "Reserve"}
          </Button>

          <Button
            variant="outline"
            onClick={() => setIsBookingMode(false)}
            className="w-full"
            disabled={isLoading}
          >
            Cancel
          </Button>

          {checkInDate && checkOutDate && (
            <div className="mt-4 border-t pt-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 underline">
                  ${price} x{" "}
                  {Math.ceil(
                    (checkOutDate.getTime() - checkInDate.getTime()) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  nights
                </span>
                <span>
                  $
                  {price *
                    Math.ceil(
                      (checkOutDate.getTime() - checkInDate.getTime()) /
                        (1000 * 60 * 60 * 24)
                    )}
                </span>
              </div>
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total</span>
                <span>${calculateTotalPrice()}</span>
              </div>
            </div>
          )}
        </div>
      ) : (
        <Button
          onClick={() => setIsBookingMode(true)}
          className="w-full bg-accent hover:bg-accent/90 text-white mb-2"
        >
          Check availability
        </Button>
      )}

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Payment Method</DialogTitle>
            <DialogDescription>
              Choose how you'd like to pay for your booking
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="card" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="card">Credit Card</TabsTrigger>
              <TabsTrigger value="wave">Wave</TabsTrigger>
            </TabsList>

            <TabsContent value="card" className="mt-4">
              <div className="flex flex-col space-y-4">
                <div className="flex items-center p-4 border rounded-md">
                  <CreditCard className="h-6 w-6 mr-2" />
                  <div>
                    <p className="font-medium">Pay with card</p>
                    <p className="text-sm text-muted-foreground">
                      Secure payment via Stripe
                    </p>
                  </div>
                </div>

                <Button
                  onClick={handleStripePayment}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? "Processing..." : "Continue to Payment"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="wave" className="mt-4">
              <div className="flex flex-col space-y-4">
                <div className="flex items-center p-4 border rounded-md">
                  <Wallet className="h-6 w-6 mr-2" />
                  <div>
                    <p className="font-medium">Pay with Wave</p>
                    <p className="text-sm text-muted-foreground">
                      Mobile money payment
                    </p>
                  </div>
                </div>

                <Button
                  onClick={handleWavePayment}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? "Processing..." : "Continue to Wave"}
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="flex justify-between">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <div>
              <p className="text-right font-semibold">Total: ${totalAmount}</p>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyBookingWidget;
