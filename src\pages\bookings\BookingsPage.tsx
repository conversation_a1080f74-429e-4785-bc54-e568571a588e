
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import Navbar from "@/components/layout/Navbar";
import {
  Tabs,
  Tabs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

type BookingType = 'property' | 'car';

interface Booking {
  id: string;
  property_id?: string;
  car_id?: string;
  check_in?: string;
  check_out?: string;
  start_date?: string;
  end_date?: string;
  total_price: number;
  status: string;
  payment_status?: string;
  properties?: {
    id: string;
    title: string;
    location: string;
  };
  cars?: {
    id: string;
    title: string;
    location: string;
  };
  created_at: string;
}

const formSchema = z.object({
  check_in: z.date({
    required_error: "Check-in date is required",
  }),
  check_out: z.date({
    required_error: "Check-out date is required",
  }),
}).refine(data => data.check_out > data.check_in, {
  message: "Check-out date must be after check-in date",
  path: ["check_out"],
});

const BookingsPage = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [propertyBookings, setPropertyBookings] = useState<Booking[]>([]);
  const [carBookings, setCarBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<BookingType>("property");
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [modificationOpen, setModificationOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  useEffect(() => {
    if (authLoading) return;
    
    if (!user) {
      navigate("/auth");
      return;
    }

    const fetchBookings = async () => {
      setIsLoading(true);
      try {
        // Fetch property bookings
        const { data: propertyData, error: propertyError } = await supabase
          .from("bookings")
          .select(`
            *,
            properties (
              id,
              title,
              location
            )
          `)
          .eq("user_id", user.id)
          .order("created_at", { ascending: false });

        if (propertyError) throw propertyError;
        setPropertyBookings(propertyData || []);

        // Fetch car bookings
        const { data: carData, error: carError } = await supabase
          .from("car_bookings")
          .select(`
            *,
            cars (
              id,
              title,
              location
            )
          `)
          .eq("user_id", user.id)
          .order("created_at", { ascending: false });

        if (carError) throw carError;
        setCarBookings(carData || []);

      } catch (error: any) {
        console.error("Error fetching bookings:", error);
        toast({
          title: "Error fetching bookings",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [user, authLoading, navigate, toast]);

  const handleModifyBooking = async (values: z.infer<typeof formSchema>) => {
    if (!selectedBooking) return;
    
    try {
      // Check availability
      const { data: isAvailable } = await supabase.rpc("check_property_availability", {
        property_id: selectedBooking.property_id,
        check_in_date: values.check_in.toISOString().split("T")[0],
        check_out_date: values.check_out.toISOString().split("T")[0],
      });

      if (!isAvailable) {
        toast({
          title: "Property not available",
          description: "This property is not available for the selected dates",
          variant: "destructive",
        });
        return;
      }

      // Update booking
      const { error } = await supabase
        .from("bookings")
        .update({
          check_in: values.check_in.toISOString().split("T")[0],
          check_out: values.check_out.toISOString().split("T")[0],
          status: "modified",
        })
        .eq("id", selectedBooking.id);

      if (error) throw error;

      // Update local state
      setPropertyBookings(prev => prev.map(booking => 
        booking.id === selectedBooking.id 
          ? { 
              ...booking, 
              check_in: values.check_in.toISOString().split("T")[0], 
              check_out: values.check_out.toISOString().split("T")[0],
              status: "modified"
            } 
          : booking
      ));

      toast({
        title: "Booking modified",
        description: "Your booking has been successfully modified",
      });

      setModificationOpen(false);
    } catch (error: any) {
      console.error("Error modifying booking:", error);
      toast({
        title: "Error modifying booking",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCancelBooking = async (booking: Booking) => {
    try {
      const table = activeTab === "property" ? "bookings" : "car_bookings";
      
      const { error } = await supabase
        .from(table)
        .update({ status: "cancelled" })
        .eq("id", booking.id);

      if (error) throw error;

      if (activeTab === "property") {
        setPropertyBookings(prev => prev.map(b => 
          b.id === booking.id ? { ...b, status: "cancelled" } : b
        ));
      } else {
        setCarBookings(prev => prev.map(b => 
          b.id === booking.id ? { ...b, status: "cancelled" } : b
        ));
      }

      toast({
        title: "Booking cancelled",
        description: "Your booking has been cancelled successfully",
      });
    } catch (error: any) {
      console.error("Error cancelling booking:", error);
      toast({
        title: "Error cancelling booking",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const openModificationDialog = (booking: Booking) => {
    if (booking.check_in && booking.check_out) {
      form.setValue("check_in", new Date(booking.check_in));
      form.setValue("check_out", new Date(booking.check_out));
    }
    setSelectedBooking(booking);
    setModificationOpen(true);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "modified":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-3xl font-bold mb-6">My Bookings</h1>
          
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as BookingType)}>
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="property">Property Bookings</TabsTrigger>
              <TabsTrigger value="car">Car Rentals</TabsTrigger>
            </TabsList>
            
            <TabsContent value="property">
              {isLoading ? (
                <div className="flex justify-center my-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
                </div>
              ) : propertyBookings.length > 0 ? (
                <Table>
                  <TableCaption>A list of your property bookings</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Property</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Check-in</TableHead>
                      <TableHead>Check-out</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {propertyBookings.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell className="font-medium">{booking.properties?.title || "Unknown"}</TableCell>
                        <TableCell>{booking.properties?.location || "Unknown"}</TableCell>
                        <TableCell>{formatDate(booking.check_in)}</TableCell>
                        <TableCell>{formatDate(booking.check_out)}</TableCell>
                        <TableCell>${booking.total_price}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(booking.status)}`}>
                            {booking.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => navigate(`/listings/${booking.property_id}`)}
                            >
                              View
                            </Button>
                            {booking.status !== "cancelled" && (
                              <>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => openModificationDialog(booking)}
                                  disabled={booking.status === "cancelled"}
                                >
                                  Modify
                                </Button>
                                <Button 
                                  variant="destructive" 
                                  size="sm"
                                  onClick={() => handleCancelBooking(booking)}
                                  disabled={booking.status === "cancelled"}
                                >
                                  Cancel
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">You don't have any property bookings yet</p>
                  <Button onClick={() => navigate("/listings")}>Browse Properties</Button>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="car">
              {isLoading ? (
                <div className="flex justify-center my-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
                </div>
              ) : carBookings.length > 0 ? (
                <Table>
                  <TableCaption>A list of your car rentals</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Car</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {carBookings.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell className="font-medium">{booking.cars?.title || "Unknown"}</TableCell>
                        <TableCell>{booking.cars?.location || "Unknown"}</TableCell>
                        <TableCell>{formatDate(booking.start_date)}</TableCell>
                        <TableCell>{formatDate(booking.end_date)}</TableCell>
                        <TableCell>${booking.total_price}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(booking.status)}`}>
                            {booking.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => navigate(`/cars/${booking.car_id}`)}
                            >
                              View
                            </Button>
                            {booking.status !== "cancelled" && (
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => handleCancelBooking(booking)}
                              >
                                Cancel
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">You don't have any car rentals yet</p>
                  <Button onClick={() => navigate("/cars")}>Browse Cars</Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Modification Dialog */}
      <Dialog open={modificationOpen} onOpenChange={setModificationOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Modify Booking</DialogTitle>
            <DialogDescription>
              Update your booking dates. This will be subject to availability.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleModifyBooking)} className="space-y-4">
              <FormField
                control={form.control}
                name="check_in"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Check-in Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="check_out"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Check-out Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => 
                            date < new Date() || 
                            (form.getValues("check_in") && date <= form.getValues("check_in"))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setModificationOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BookingsPage;
