
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";
import PropertyCard from "../properties/PropertyCard";

const FeaturedListings = () => {
  const { data: properties, isLoading } = useQuery({
    queryKey: ['featured-properties'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .limit(4);

      if (error) throw error;

      return data.map(property => ({
        id: property.id,
        title: property.title,
        location: property.location,
        price: Number(property.price),
        rating: 4.7, // Temporary hardcoded rating
        reviews: 100, // Temporary hardcoded reviews
        image: property.images?.[0] || '/placeholder.svg',
        beds: Number(property.beds),
        baths: Number(property.baths),
        isSuperHost: false,
      }));
    }
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-brown">Featured Stays</h2>
          <Link
            to="/listings"
            className="text-secondary font-medium hover:text-secondary/80 transition-colors"
          >
            View all
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {properties?.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedListings;
