
import { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { toast } from "sonner";

type Message = {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  timestamp: Date;
  read: boolean;
};

type Contact = {
  id: string;
  name: string;
  avatar_url: string | null;
  last_message: string;
  last_message_time: Date;
  unread_count: number;
};

// Mock data for demonstration purposes
const mockContacts: Contact[] = [
  {
    id: "1",
    name: "<PERSON>",
    avatar_url: null,
    last_message: "Hi, I'm interested in your property...",
    last_message_time: new Date(2025, 4, 15, 14, 30),
    unread_count: 2,
  },
  {
    id: "2",
    name: "<PERSON>",
    avatar_url: null,
    last_message: "What time is check-in?",
    last_message_time: new Date(2025, 4, 15, 10, 15),
    unread_count: 0,
  },
  {
    id: "3",
    name: "Mike Johnson",
    avatar_url: null,
    last_message: "Thanks for accepting my booking!",
    last_message_time: new Date(2025, 4, 14, 18, 45),
    unread_count: 0,
  },
  {
    id: "4",
    name: "Sarah Williams",
    avatar_url: null,
    last_message: "Is there parking available?",
    last_message_time: new Date(2025, 4, 13, 9, 20),
    unread_count: 0,
  },
];

// Mock conversation data
const mockConversation: Message[] = [
  {
    id: "msg1",
    sender_id: "guest-1",
    receiver_id: "host-1",
    content: "Hi, I'm interested in your beach house property for next weekend.",
    timestamp: new Date(2025, 4, 15, 14, 15),
    read: true,
  },
  {
    id: "msg2",
    sender_id: "host-1",
    receiver_id: "guest-1",
    content: "Hello! Thank you for your interest. The beach house is available for next weekend.",
    timestamp: new Date(2025, 4, 15, 14, 20),
    read: true,
  },
  {
    id: "msg3",
    sender_id: "guest-1",
    receiver_id: "host-1",
    content: "Great! I have a few questions about amenities. Does it have WiFi and air conditioning?",
    timestamp: new Date(2025, 4, 15, 14, 25),
    read: true,
  },
  {
    id: "msg4",
    sender_id: "host-1",
    receiver_id: "guest-1",
    content: "Yes, the property has high-speed WiFi and central air conditioning throughout. There's also a fully equipped kitchen and a BBQ grill on the deck.",
    timestamp: new Date(2025, 4, 15, 14, 28),
    read: true,
  },
  {
    id: "msg5",
    sender_id: "guest-1",
    receiver_id: "host-1",
    content: "That sounds perfect! Is it walking distance to the beach?",
    timestamp: new Date(2025, 4, 15, 14, 30),
    read: false,
  },
];

const HostMessages = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>(mockContacts);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [conversation, setConversation] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user) {
      // In a real implementation, we would fetch actual messages here
      setLoading(true);
      // Simulate loading
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  }, [user]);

  useEffect(() => {
    if (selectedContact) {
      loadConversation(selectedContact.id);
    }
  }, [selectedContact]);

  const loadConversation = (contactId: string) => {
    // In a real implementation, we would fetch conversation from the database
    setConversation(mockConversation);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedContact) return;
    
    setSendingMessage(true);
    
    // In a real implementation, we would send the message to the API
    setTimeout(() => {
      // Add the new message to the conversation
      const newMsg: Message = {
        id: `msg${conversation.length + 1}`,
        sender_id: user?.id || 'host-1',
        receiver_id: selectedContact.id,
        content: newMessage,
        timestamp: new Date(),
        read: false
      };
      
      setConversation([...conversation, newMsg]);
      setNewMessage("");
      setSendingMessage(false);
    }, 500);
  };

  const formatTime = (date: Date) => {
    return format(date, 'h:mm a');
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return format(date, 'MMM d, yyyy');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Messages</h1>
      
      <div className="flex flex-col md:flex-row h-[calc(100vh-220px)] border rounded-lg overflow-hidden">
        {/* Contacts list */}
        <div className="w-full md:w-1/3 border-r">
          <div className="p-3 border-b">
            <Input 
              placeholder="Search messages..." 
              className="w-full"
            />
          </div>
          
          <ScrollArea className="h-[calc(100%-52px)]">
            {contacts.map(contact => (
              <div 
                key={contact.id}
                className={`flex items-center p-3 gap-3 cursor-pointer hover:bg-gray-100 ${
                  selectedContact?.id === contact.id ? 'bg-gray-100' : ''
                }`}
                onClick={() => setSelectedContact(contact)}
              >
                <Avatar>
                  <AvatarImage src={contact.avatar_url || ""} />
                  <AvatarFallback>{contact.name.charAt(0)}</AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium truncate">{contact.name}</h3>
                    <span className="text-xs text-gray-500">
                      {formatDate(contact.last_message_time)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{contact.last_message}</p>
                </div>
                
                {contact.unread_count > 0 && (
                  <span className="bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {contact.unread_count}
                  </span>
                )}
              </div>
            ))}
          </ScrollArea>
        </div>
        
        {/* Conversation */}
        <div className="flex-1 flex flex-col">
          {selectedContact ? (
            <>
              {/* Header */}
              <div className="p-3 border-b flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={selectedContact.avatar_url || ""} />
                  <AvatarFallback>{selectedContact.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="font-medium">{selectedContact.name}</h2>
                  <p className="text-xs text-gray-500">Guest</p>
                </div>
              </div>
              
              {/* Messages */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {conversation.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sender_id === user?.id || message.sender_id === 'host-1'
                          ? 'justify-end'
                          : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg p-3 ${
                          message.sender_id === user?.id || message.sender_id === 'host-1'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100'
                        }`}
                      >
                        <div className="text-sm">{message.content}</div>
                        <div
                          className={`text-xs mt-1 ${
                            message.sender_id === user?.id || message.sender_id === 'host-1'
                              ? 'text-blue-50'
                              : 'text-gray-500'
                          }`}
                        >
                          {formatTime(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              
              {/* Message Input */}
              <div className="p-3 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button 
                    disabled={!newMessage.trim() || sendingMessage} 
                    onClick={handleSendMessage}
                  >
                    {sendingMessage ? "Sending..." : "Send"}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <p>Select a conversation to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-4 text-center text-sm text-muted-foreground">
        <p>
          This is a UI demonstration. Real messaging functionality will be implemented in the future
          with database integration and real-time updates.
        </p>
      </div>
    </div>
  );
};

export default HostMessages;
