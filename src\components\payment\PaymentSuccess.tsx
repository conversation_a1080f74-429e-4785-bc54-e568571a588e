
import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/layout/Navbar";

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [bookingDetails, setBookingDetails] = useState<any>(null);

  const sessionId = searchParams.get("session_id");
  const bookingId = searchParams.get("booking_id");

  useEffect(() => {
    const verifyPayment = async () => {
      if (!sessionId || !bookingId) {
        toast({
          title: "Missing information",
          description: "Could not verify payment due to missing information.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // Verify the payment status
        const { data, error } = await supabase.functions.invoke("verify-stripe-payment", {
          body: { sessionId, bookingId }
        });

        if (error) throw error;

        if (data.paymentStatus === "completed") {
          // Fetch booking details
          const { data: bookingData, error: bookingError } = await supabase
            .from("bookings")
            .select(`
              *,
              properties(*),
              profiles:user_id(*)
            `)
            .eq("id", bookingId)
            .single();

          if (bookingError) throw bookingError;
          setBookingDetails(bookingData);

          // Send confirmation emails
          await supabase.functions.invoke("send-booking-confirmation", {
            body: { bookingId }
          });

          toast({
            title: "Payment successful!",
            description: "Your booking has been confirmed and emails have been sent.",
          });
        } else {
          toast({
            title: "Payment processing",
            description: "Your payment is being processed. We'll notify you when it's complete.",
          });
        }
      } catch (error: any) {
        console.error("Payment verification error:", error);
        toast({
          title: "Verification failed",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId, bookingId, toast]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-800">Payment Successful!</h1>
            <p className="text-gray-600 mt-2">Your booking has been confirmed.</p>
          </div>

          {loading ? (
            <div className="flex justify-center my-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
            </div>
          ) : bookingDetails ? (
            <div className="space-y-6">
              <div className="border-b pb-4">
                <h2 className="text-xl font-semibold mb-2">Booking Details</h2>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Property:</span>
                    <span className="font-medium">{bookingDetails.properties?.title}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span>{bookingDetails.properties?.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Check-in:</span>
                    <span>{new Date(bookingDetails.check_in).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Check-out:</span>
                    <span>{new Date(bookingDetails.check_out).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Amount:</span>
                    <span className="font-semibold">${bookingDetails.total_price}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <p className="text-gray-700">
                  A confirmation email has been sent to your email address. 
                  Check your inbox for all the details of your booking.
                </p>
              </div>

              <div className="flex space-x-4">
                <Button 
                  onClick={() => navigate("/bookings")}
                  className="flex-1"
                >
                  View All Bookings
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => navigate("/")}
                  className="flex-1"
                >
                  Return Home
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center my-8">
              <p className="text-gray-700">No booking details available</p>
              <Button 
                onClick={() => navigate("/")}
                className="mt-4"
              >
                Return Home
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default PaymentSuccess;
