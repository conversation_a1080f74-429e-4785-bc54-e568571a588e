
import { useState } from "react";
import { <PERSON>, User } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import AddReviewForm from "./AddReviewForm";
import { useAuth } from "@/components/layout/AuthProvider";
import { useReviews } from "@/hooks/useReviews";

interface ReviewsProps {
  propertyId: string;
}

const Reviews = ({ propertyId }: ReviewsProps) => {
  const { user } = useAuth();
  const [showAddReview, setShowAddReview] = useState(false);

  const { data: reviews, isLoading } = useReviews(propertyId);

  if (isLoading) {
    return <div className="animate-pulse h-32 bg-gray-100 rounded-lg"></div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          Reviews ({reviews?.length || 0})
        </h2>
        {user && !showAddReview && (
          <Button onClick={() => setShowAddReview(true)}>
            Write a Review
          </Button>
        )}
      </div>

      {showAddReview && (
        <AddReviewForm 
          propertyId={propertyId} 
          onCancel={() => setShowAddReview(false)}
        />
      )}

      <div className="space-y-6">
        {reviews?.map((review) => (
          <div key={review.id} className="border-b pb-6">
            <div className="flex items-center space-x-4 mb-4">
              <Avatar>
                <AvatarImage 
                  src={review.profiles?.avatar_url || undefined} 
                  alt={review.profiles?.first_name || 'User'} 
                />
                <AvatarFallback>
                  <User className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">
                  {review.profiles?.first_name} {review.profiles?.last_name}
                </p>
                <div className="flex items-center">
                  {Array.from({ length: review.rating }).map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600">{review.comment}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Reviews;
