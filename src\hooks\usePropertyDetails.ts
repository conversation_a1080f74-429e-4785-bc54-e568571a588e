import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const usePropertyDetails = (id: string | undefined) => {
  return useQuery({
    queryKey: ["property", id],
    queryFn: async () => {
      if (!id) throw new Error("Property ID is required");

      // Fetch property details
      const { data, error } = await supabase
        .from("properties")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;

      // Fetch reviews for this property
      const { data: reviewsData, error: reviewsError } = await supabase
        .from("reviews")
        .select("rating")
        .eq("property_id", id);

      if (reviewsError) throw reviewsError;

      const reviews = reviewsData || [];
      const reviewCount = reviews.length;
      const avgRating =
        reviewCount > 0
          ? reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviewCount
          : 0;

      return {
        id: data.id,
        title: data.title,
        location: data.location,
        price: Number(data.price),
        rating: avgRating,
        reviews: reviewCount,
        images: data.images || [],
        beds: Number(data.beds),
        baths: Number(data.baths),
        description: data.description,
        isSuperHost: false,
      };
    },
    enabled: !!id,
  });
};
