
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { useAuth } from "@/components/layout/AuthProvider";
import { PaymentMethod } from "./PaymentMethodsList";

const payoutRequestSchema = z.object({
  amount: z.string().refine(
    (val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    },
    { message: "Amount must be a positive number" }
  ),
  notes: z.string().optional(),
});

interface PayoutRequestFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  paymentMethods: PaymentMethod[];
  availableBalance: number;
}

const PayoutRequestForm = ({ 
  open, 
  onClose, 
  onSuccess, 
  paymentMethods, 
  availableBalance 
}: PayoutRequestFormProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof payoutRequestSchema>>({
    resolver: zodResolver(payoutRequestSchema),
    defaultValues: {
      amount: "",
      notes: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof payoutRequestSchema>) => {
    if (!user) return;
    
    if (paymentMethods.length === 0) {
      toast.error("Please add a payment method first");
      onClose();
      return;
    }
    
    const amount = parseFloat(values.amount);
    if (amount > availableBalance) {
      toast.error(`Amount exceeds your available balance of $${availableBalance.toFixed(2)}`);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Find default payment method
      const defaultMethod = paymentMethods.find(method => method.is_default) || paymentMethods[0];
      
      const { data, error } = await supabase
        .from("payout_requests")
        .insert({
          host_id: user.id,
          amount: amount,
          notes: values.notes || null,
          payment_method_id: defaultMethod.id,
          status: "pending"
        })
        .select("*")
        .single();
        
      if (error) throw error;
      
      toast.success("Payout request submitted successfully");
      form.reset();
      if (onSuccess) onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error submitting payout request:", error);
      toast.error(`Failed to submit payout request: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Request Payout</DialogTitle>
          <DialogDescription>
            Request a payout to your {paymentMethods.length > 0 ? 'default payment method' : 'account'}.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount ($)</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" placeholder="Enter amount" {...field} />
                  </FormControl>
                  <FormDescription>
                    Available balance: ${availableBalance.toFixed(2)}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Any additional information" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button 
                type="submit" 
                disabled={isSubmitting || paymentMethods.length === 0}
              >
                {isSubmitting ? "Processing..." : "Submit Request"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PayoutRequestForm;
