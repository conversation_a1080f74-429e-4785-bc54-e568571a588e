
import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DollarSign } from "lucide-react";
import { format } from "date-fns";

export interface PlatformEarning {
  id: string;
  booking_id: string | null;
  car_booking_id: string | null;
  booking_type: string;
  total_booking_amount: number;
  platform_fee: number;
  host_payout_amount: number;
  created_at: string;
}

export interface EarningsSummary {
  totalPlatformFees: number;
  totalBookingAmount: number;
  totalTransactions: number;
}

export interface PlatformFeesTableProps {
  earnings: PlatformEarning[];
  summary: EarningsSummary;
}

const PlatformFeesTable: React.FC<PlatformFeesTableProps> = ({ earnings, summary }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Earnings</CardTitle>
        <CardDescription>
          Overview of platform fees collected from bookings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-primary/10 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-primary" />
              <span className="text-sm font-medium">Total Fees</span>
            </div>
            <p className="text-2xl font-bold mt-2">${summary.totalPlatformFees.toFixed(2)}</p>
          </div>
          <div className="bg-primary/10 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-primary" />
              <span className="text-sm font-medium">Total Bookings Value</span>
            </div>
            <p className="text-2xl font-bold mt-2">${summary.totalBookingAmount.toFixed(2)}</p>
          </div>
          <div className="bg-primary/10 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-primary" />
              <span className="text-sm font-medium">Total Transactions</span>
            </div>
            <p className="text-2xl font-bold mt-2">{summary.totalTransactions}</p>
          </div>
        </div>

        <Table>
          <TableCaption>A list of all platform earnings from bookings</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Booking Type</TableHead>
              <TableHead>Booking Amount</TableHead>
              <TableHead>Platform Fee (10%)</TableHead>
              <TableHead>Host Payout</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {earnings.map((earning) => (
              <TableRow key={earning.id}>
                <TableCell>{format(new Date(earning.created_at), 'MMM dd, yyyy')}</TableCell>
                <TableCell className="capitalize">{earning.booking_type}</TableCell>
                <TableCell>${earning.total_booking_amount.toFixed(2)}</TableCell>
                <TableCell>${earning.platform_fee.toFixed(2)}</TableCell>
                <TableCell>${earning.host_payout_amount.toFixed(2)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default PlatformFeesTable;
