import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  <PERSON><PERSON>Header, 
  DialogTitle 
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Database } from "@/integrations/supabase/types";

interface PropertyBooking {
  id: string;
  property_id: string;
  user_id: string;
  check_in: string;
  check_out: string;
  total_price: number;
  created_at: string;
  updated_at: string;
  status: string;
  payment_status: string;
  property_title: string;
  user_name: string;
  properties: {
    title: string;
    location: string;
  };
}

interface CarBooking {
  id: string;
  car_id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  duration_type: Database['public']['Enums']['rental_duration'];
  total_price: number;
  created_at: string;
  updated_at: string;
  status: string;
  car_title: string;
  user_name: string;
  payment_status: string;
  cars: {
    title: string;
    make: string;
    model: string;
  };
}

type Booking = PropertyBooking | CarBooking;

const AdminBookings = () => {
  const [propertyBookings, setPropertyBookings] = useState<PropertyBooking[]>([]);
  const [carBookings, setCarBookings] = useState<CarBooking[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [detailOpen, setDetailOpen] = useState(false);
  const [bookingType, setBookingType] = useState<"property" | "car">("property");

  useEffect(() => {
    fetchBookings();
  }, [activeTab]);

  const fetchBookings = async () => {
    setLoading(true);
    try {
      // Fetch property bookings
      let propertyQuery = supabase
        .from('bookings')
        .select(`
          *,
          properties:property_id (title, location)
        `);
      
      if (activeTab === "pending") {
        propertyQuery = propertyQuery.eq('status', 'pending');
      } else if (activeTab === "confirmed") {
        propertyQuery = propertyQuery.eq('status', 'confirmed');
      } else if (activeTab === "cancelled") {
        propertyQuery = propertyQuery.eq('status', 'cancelled');
      }
      
      const { data: propertyData, error: propertyError } = await propertyQuery;
      
      if (propertyError) throw propertyError;
      
      // Process property booking data
      const enhancedPropertyBookings = await Promise.all((propertyData || []).map(async (booking) => {
        // Get user profile details
        const { data: profileData } = await supabase
          .from('profiles')
          .select('first_name, last_name')
          .eq('id', booking.user_id)
          .single();
        
        return {
          ...booking,
          property_title: booking.properties?.title || 'Unknown Property',
          user_name: profileData 
            ? `${profileData.first_name || ''} ${profileData.last_name || ''}`.trim() 
            : 'Unknown User'
        };
      }));
      
      // Fetch car bookings
      let carQuery = supabase
        .from('car_bookings')
        .select(`
          *,
          cars:car_id (title, make, model)
        `);
      
      if (activeTab === "pending") {
        carQuery = carQuery.eq('status', 'pending');
      } else if (activeTab === "confirmed") {
        carQuery = carQuery.eq('status', 'confirmed');
      } else if (activeTab === "cancelled") {
        carQuery = carQuery.eq('status', 'cancelled');
      }
      
      const { data: carData, error: carError } = await carQuery;
      
      if (carError) throw carError;
      
      // Process car booking data
      const enhancedCarBookings = await Promise.all((carData || []).map(async (booking) => {
        // Get user profile details
        const { data: profileData } = await supabase
          .from('profiles')
          .select('first_name, last_name')
          .eq('id', booking.user_id)
          .single();
        
        return {
          ...booking,
          car_title: booking.cars?.title || `${booking.cars?.make || ''} ${booking.cars?.model || ''}`.trim() || 'Unknown Car',
          user_name: profileData 
            ? `${profileData.first_name || ''} ${profileData.last_name || ''}`.trim() 
            : 'Unknown User',
          payment_status: 'pending' // Adding this to match the PropertyBooking interface
        };
      }));
      
      setPropertyBookings(enhancedPropertyBookings);
      setCarBookings(enhancedCarBookings);
    } catch (error: any) {
      toast.error("Failed to fetch bookings: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const viewDetails = (booking: Booking, type: "property" | "car") => {
    setSelectedBooking(booking);
    setBookingType(type);
    setDetailOpen(true);
  };

  const updateBookingStatus = async (id: string, status: string) => {
    try {
      let result;
      
      if (bookingType === "property") {
        result = await supabase
          .from('bookings')
          .update({ status })
          .eq('id', id);
      } else {
        result = await supabase
          .from('car_bookings')
          .update({ status })
          .eq('id', id);
      }
      
      if (result.error) throw result.error;
      
      toast.success(`Booking ${status} successfully`);
      setDetailOpen(false);
      fetchBookings();
    } catch (error: any) {
      toast.error("Failed to update booking: " + error.message);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return dateString || 'N/A';
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Manage Bookings</h1>
      
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Bookings</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="confirmed">Confirmed</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>
      </Tabs>
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      ) : (
        <>
          <h2 className="text-xl font-semibold mb-4">Property Bookings ({propertyBookings.length})</h2>
          <div className="rounded-md border mb-8">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Guest</TableHead>
                  <TableHead>Check-in</TableHead>
                  <TableHead>Check-out</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {propertyBookings.length > 0 ? (
                  propertyBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.property_title}</TableCell>
                      <TableCell>{booking.user_name}</TableCell>
                      <TableCell>{formatDate(booking.check_in)}</TableCell>
                      <TableCell>{formatDate(booking.check_out)}</TableCell>
                      <TableCell>${booking.total_price}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            booking.status === 'confirmed' ? 'default' : 
                            booking.status === 'cancelled' ? 'destructive' : 'outline'
                          }
                        >
                          {booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            booking.payment_status === 'completed' ? 'default' : 
                            booking.payment_status === 'failed' ? 'destructive' : 'outline'
                          }
                        >
                          {booking.payment_status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => viewDetails(booking, "property")}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      No property bookings found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          <h2 className="text-xl font-semibold mb-4">Car Bookings ({carBookings.length})</h2>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Car</TableHead>
                  <TableHead>Renter</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {carBookings.length > 0 ? (
                  carBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.car_title}</TableCell>
                      <TableCell>{booking.user_name}</TableCell>
                      <TableCell>{formatDate(booking.start_date)}</TableCell>
                      <TableCell>{formatDate(booking.end_date)}</TableCell>
                      <TableCell>{booking.duration_type}</TableCell>
                      <TableCell>${booking.total_price}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            booking.status === 'confirmed' ? 'default' : 
                            booking.status === 'cancelled' ? 'destructive' : 'outline'
                          }
                        >
                          {booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => viewDetails(booking, "car")}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      No car bookings found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}
      
      {/* Booking Detail Dialog */}
      <Dialog open={detailOpen} onOpenChange={setDetailOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
          </DialogHeader>
          
          {selectedBooking && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    {bookingType === 'property' ? 'Property' : 'Car'}
                  </p>
                  <p className="text-lg font-semibold">
                    {bookingType === 'property' 
                      ? (selectedBooking as PropertyBooking).property_title 
                      : (selectedBooking as CarBooking).car_title
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    {bookingType === 'property' ? 'Guest' : 'Renter'}
                  </p>
                  <p className="text-lg font-semibold">{selectedBooking.user_name}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    {bookingType === 'property' ? 'Check-in' : 'Start Date'}
                  </p>
                  <p className="text-base">
                    {bookingType === 'property' 
                      ? formatDate((selectedBooking as PropertyBooking).check_in) 
                      : formatDate((selectedBooking as CarBooking).start_date)
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    {bookingType === 'property' ? 'Check-out' : 'End Date'}
                  </p>
                  <p className="text-base">
                    {bookingType === 'property' 
                      ? formatDate((selectedBooking as PropertyBooking).check_out) 
                      : formatDate((selectedBooking as CarBooking).end_date)
                    }
                  </p>
                </div>
              </div>
              
              {bookingType === 'car' && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Duration Type</p>
                  <p className="text-base capitalize">
                    {(selectedBooking as CarBooking).duration_type}
                  </p>
                </div>
              )}
              
              <div>
                <p className="text-sm font-medium text-gray-500">Total Amount</p>
                <p className="text-xl font-semibold">${selectedBooking.total_price}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Current Status</p>
                <Badge 
                  className="mt-1"
                  variant={
                    selectedBooking.status === 'confirmed' ? 'default' : 
                    selectedBooking.status === 'cancelled' ? 'destructive' : 'outline'
                  }
                >
                  {selectedBooking.status}
                </Badge>
              </div>
              
              {bookingType === 'property' && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Payment Status</p>
                  <Badge 
                    className="mt-1"
                    variant={
                      (selectedBooking as PropertyBooking).payment_status === 'completed' ? 'default' : 
                      (selectedBooking as PropertyBooking).payment_status === 'failed' ? 'destructive' : 'outline'
                    }
                  >
                    {(selectedBooking as PropertyBooking).payment_status}
                  </Badge>
                </div>
              )}
              
              <div className="pt-4 border-t flex justify-end space-x-2">
                {selectedBooking.status === 'pending' && (
                  <>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => updateBookingStatus(selectedBooking.id, 'cancelled')}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => updateBookingStatus(selectedBooking.id, 'confirmed')}
                    >
                      Confirm
                    </Button>
                  </>
                )}
                {selectedBooking.status === 'confirmed' && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => updateBookingStatus(selectedBooking.id, 'cancelled')}
                  >
                    Cancel
                  </Button>
                )}
                {selectedBooking.status === 'cancelled' && (
                  <Button
                    size="sm"
                    onClick={() => updateBookingStatus(selectedBooking.id, 'confirmed')}
                  >
                    Restore
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminBookings;
