const AboutSection = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-brown mb-6">
            Africa's Trusted Home for Travel
          </h2>
          <p className="text-lg text-gray-700 mb-8 leading-relaxed">
            Gescostay is more than a booking platform — it's a movement to transform how Africa travels, hosts, and connects with the world.
          </p>
          <p className="text-lg text-gray-700 mb-8 leading-relaxed">
            We're here to redefine what it means to explore Africa: authentic stays, trusted communities, and seamless experiences powered by technology that understands the continent's heart. Whether you're a traveller, a host, or part of the diaspora coming home — Gescostay is your platform.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">🌍</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Travelers</h3>
              <p className="text-gray-600">
                Discover authentic African experiences with verified hosts and secure bookings.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">🏠</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Hosts</h3>
              <p className="text-gray-600">
                Share your space, earn income, and connect with travelers from around the world.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-brown rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">❤️</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Diaspora</h3>
              <p className="text-gray-600">
                Reconnect with your roots through trusted accommodations and local experiences.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
