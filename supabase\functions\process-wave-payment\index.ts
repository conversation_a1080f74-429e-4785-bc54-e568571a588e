import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY") || "";
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const WAVE_API_KEY = Deno.env.get("WAVE_API_KEY") || "";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
    });
  }
  try {
    const { bookingId, bookingType, amount, returnUrl, userEmail, userMobile } =
      await req.json();
    // Create Supabase client
    const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false,
        },
      }
    );
    // Get auth header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Missing Authorization header");
    }
    const token = authHeader.replace("Bearer ", "");
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser(token);
    if (userError || !user) {
      throw new Error("Unauthorized");
    }
    console.log("Creating Wave checkout session");
    // Determine the booking table and ID field based on booking type
    const bookingTable = bookingType === "car" ? "car_bookings" : "bookings";
    const bookingIdField = bookingType === "car" ? "car_id" : "property_id";
    // Get booking details to include in the payment description
    console.log("Before getting booking details");
    const { data: bookingData, error: bookingError } = await supabaseAdmin
      .from(bookingTable)
      .select(
        `*, ${bookingType === "car" ? "cars(title)" : "properties(title)"}`
      )
      .eq("id", bookingId)
      .single();
    if (bookingError) {
      throw new Error(
        `Error fetching booking details: ${bookingError.message}`
      );
    }
    // Create a descriptive client reference
    const clientReference = `gescostay-${bookingType}-${bookingId}`;
    // Prepare Wave payload
    const wavePayload = {
      amount: amount.toString(),
      currency: "GMD",
      error_url: `${returnUrl}/payment-cancelled?booking_id=${bookingId}&type=${bookingType}`,
      success_url: `${returnUrl}/payment-success?checkout_id={CHECKOUT_SESSION_ID}&booking_id=${bookingId}&type=${bookingType}`,
      client_reference: clientReference,
    };
    console.log("Wave API payload:", JSON.stringify(wavePayload));
    // Create Wave checkout session
    console.log("Creating Wave checkout session api");
    const response = await fetch("https://api.wave.com/v1/checkout/sessions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${WAVE_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(wavePayload),
    });
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { parseError: true, text: await response.text() };
      }
      console.error("Wave API error details:", errorData);
      throw new Error(
        `Wave API error: ${errorData.message || response.statusText}`
      );
    }
    const waveSession = await response.json();
    // Log payment attempt
    await supabaseAdmin.from("payment_logs").insert({
      booking_id: bookingId,
      payment_method: "wave",
      amount,
      status: "pending",
      transaction_id: waveSession.id,
      provider_response: waveSession,
    });
    // Update booking with payment information
    await supabaseAdmin
      .from(bookingTable)
      .update({
        payment_method: "wave",
        payment_status: "pending",
        wave_checkout_id: waveSession.id,
      })
      .eq("id", bookingId);
    return new Response(
      JSON.stringify({
        success: true,
        checkoutId: waveSession.id,
        checkoutUrl: waveSession.wave_launch_url,
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error processing Wave payment:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 500,
      }
    );
  }
});
