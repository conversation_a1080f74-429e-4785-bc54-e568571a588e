
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export type InsuranceOption = {
  id: string;
  name: string;
  description: string;
  price_day: number;
  coverage_details: {
    liability_limit?: string;
    collision_deductible?: string;
    comprehensive?: boolean;
    roadside_assistance?: boolean;
  };
};

export const useInsuranceOptions = () => {
  return useQuery({
    queryKey: ["insurance-options"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("car_insurance_options")
        .select("*");

      if (error) throw error;

      // Convert the JSON data to the expected type
      return (data || []).map(item => ({
        ...item,
        coverage_details: item.coverage_details as InsuranceOption['coverage_details']
      })) as InsuranceOption[];
    },
  });
};
