
import { Link } from "react-router-dom";
import { CarListing } from "@/hooks/useCarListings";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Car, MapPin } from "lucide-react";

interface CarCardProps {
  car: CarListing;
}

const CarCard = ({ car }: CarCardProps) => {
  return (
    <Link to={`/cars/${car.id}`}>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow property-card-transition">
        <div className="relative h-48">
          <img
            src={car.images?.[0] || "/placeholder.svg"}
            alt={car.title}
            className="w-full h-full object-cover"
          />
          <Badge className="absolute top-2 right-2 bg-accent text-white">
            {car.car_type.charAt(0).toUpperCase() + car.car_type.slice(1)}
          </Badge>
        </div>
        
        <CardContent className="pt-4">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
            <MapPin className="h-4 w-4" />
            <span>{car.location}</span>
          </div>
          
          <h3 className="text-lg font-semibold line-clamp-1">{car.title}</h3>
          
          <div className="flex items-center gap-4 mt-2 text-sm">
            <div className="flex items-center gap-1">
              <Car className="h-4 w-4" />
              <span>{car.make} {car.model}</span>
            </div>
            <div>
              <span>{car.year}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2 mt-2">
            <span>{car.seats} Seats</span>
            <span>•</span>
            <span>{car.transmission}</span>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between pt-0 border-t">
          <div className="text-xl font-semibold">${car.price_day}<span className="text-sm font-normal">/day</span></div>
          <div className="text-sm text-muted-foreground">
            From ${car.price_week}/week
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
};

export default CarCard;
