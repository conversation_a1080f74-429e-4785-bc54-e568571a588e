
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export interface PayoutRequest {
  id: string;
  host_id: string;
  amount: number;
  status: string;
  notes: string | null;
  admin_notes: string | null;
  created_at: string;
  updated_at: string;
  payment_method_id: string | null;
  processed_at: string | null;
  processed_by: string | null;
  payment_method?: {
    provider: string;
    account_id: string;
    status: string;
  };
}

interface PayoutRequestsListProps {
  payoutRequests: PayoutRequest[];
  onRequestPayout: () => void;
}

const PayoutRequestsList = ({ payoutRequests, onRequestPayout }: PayoutRequestsListProps) => {
  if (payoutRequests.length === 0) {
    return (
      <div className="text-center p-8 border rounded-lg">
        <p className="text-muted-foreground">No payout requests yet.</p>
        <Button 
          className="mt-4"
          onClick={onRequestPayout}
        >
          Request a Payout
        </Button>
      </div>
    );
  }
  
  return (
    <>
      {payoutRequests.map((request) => (
        <Card key={request.id}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-lg">${parseFloat(request.amount.toString()).toFixed(2)}</CardTitle>
                <CardDescription>
                  Requested on {new Date(request.created_at).toLocaleDateString()}
                </CardDescription>
              </div>
              <Badge variant={
                request.status === "approved" ? "default" : 
                request.status === "pending" ? "secondary" : 
                "outline"
              }>
                {request.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {request.notes && (
              <p className="text-sm text-muted-foreground mb-2">
                Note: {request.notes}
              </p>
            )}
            {request.admin_notes && (
              <div className="bg-gray-50 p-2 rounded text-sm">
                <p className="font-medium">Admin Note:</p>
                <p>{request.admin_notes}</p>
              </div>
            )}
            {request.payment_method && (
              <div className="text-xs text-muted-foreground mt-2">
                Payment via: {request.payment_method.provider} ({request.payment_method.account_id})
              </div>
            )}
          </CardContent>
        </Card>
      ))}
      
      <div className="flex justify-center mt-4">
        <Button 
          onClick={onRequestPayout}
        >
          Request New Payout
        </Button>
      </div>
    </>
  );
};

export default PayoutRequestsList;
