import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/layout/AuthProvider";
import { Loader2 } from "lucide-react";
import Navbar from "@/components/layout/Navbar";

const AuthPage = () => {
  const [searchParams] = useSearchParams();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [loading, setLoading] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const { toast } = useToast();
  const { signIn, signUp, resetPassword } = useAuth();

  // Set initial mode based on URL parameter
  useEffect(() => {
    const mode = searchParams.get("mode");
    if (mode === "signup") {
      setIsLogin(false);
    } else if (mode === "signin") {
      setIsLogin(true);
    }
  }, [searchParams]);

  const clearForm = () => {
    setEmail("");
    setPassword("");
    setFirstName("");
    setLastName("");
    setIsResetPassword(false);
  };

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      if (isResetPassword) {
        await resetPassword(email);
        toast({
          title: "Success!",
          description:
            "Please check your email for password reset instructions.",
        });
        clearForm();
      } else if (isLogin) {
        await signIn(email, password);
        clearForm();
      } else {
        await signUp(email, password, firstName, lastName);
        toast({
          title: "Success!",
          description: "Please check your email to verify your account.",
        });
        clearForm();
      }
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-brown">
            {isResetPassword
              ? "Reset your password"
              : isLogin
              ? "Sign in to your account"
              : "Create a new account"}
          </h2>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form className="space-y-6" onSubmit={handleAuth}>
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email address
                </label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1"
                  disabled={loading}
                />
              </div>

              {!isResetPassword && (
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Password
                  </label>
                  <Input
                    id="password"
                    type="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="mt-1"
                    disabled={loading}
                  />
                </div>
              )}

              {!isLogin && !isResetPassword && (
                <>
                  <div>
                    <label
                      htmlFor="firstName"
                      className="block text-sm font-medium text-gray-700"
                    >
                      First Name
                    </label>
                    <Input
                      id="firstName"
                      type="text"
                      required
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="lastName"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Last Name
                    </label>
                    <Input
                      id="lastName"
                      type="text"
                      required
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>
                </>
              )}

              <Button
                type="submit"
                className="w-full bg-accent hover:bg-accent/90"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isResetPassword
                      ? "Sending reset link..."
                      : isLogin
                      ? "Signing in..."
                      : "Signing up..."}
                  </>
                ) : isResetPassword ? (
                  "Send reset link"
                ) : isLogin ? (
                  "Sign in"
                ) : (
                  "Sign up"
                )}
              </Button>
            </form>

            <div className="mt-6">
              <Button
                variant="link"
                className="w-full text-accent"
                onClick={() => {
                  setIsLogin(!isLogin);
                  setIsResetPassword(false);
                  clearForm();
                }}
                disabled={loading}
              >
                {isLogin
                  ? "Need an account? Sign up"
                  : "Already have an account? Sign in"}
              </Button>

              {isLogin && (
                <Button
                  variant="link"
                  className="w-full text-accent"
                  onClick={() => setIsResetPassword(true)}
                  disabled={loading}
                >
                  Forgot your password?
                </Button>
              )}

              {isResetPassword && (
                <Button
                  variant="link"
                  className="w-full text-accent"
                  onClick={() => {
                    setIsResetPassword(false);
                    clearForm();
                  }}
                  disabled={loading}
                >
                  Back to sign in
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
