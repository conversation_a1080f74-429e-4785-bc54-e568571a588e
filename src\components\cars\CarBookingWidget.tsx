import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useInsuranceOptions,
  InsuranceOption,
} from "@/hooks/useInsuranceOptions";
import { useCheckCarAvailability } from "@/hooks/useCheckCarAvailability";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useNavigate } from "react-router-dom";
import { Database } from "@/integrations/supabase/types";
import { PlatformFeeService } from "@/services/PlatformFeeService";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { useCarBookedDates } from "@/hooks/useBookedDates";
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, ShieldCheck, CreditCard, Wallet } from "lucide-react";

interface CarBookingWidgetProps {
  carId: string;
  carTitle: string;
  priceDay: number;
  priceWeek: number;
  priceMonth: number;
}

type DurationType = Database["public"]["Enums"]["rental_duration"];

const CarBookingWidget = ({
  carId,
  carTitle,
  priceDay,
  priceWeek,
  priceMonth,
}: CarBookingWidgetProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [durationType, setDurationType] = useState<DurationType>("day");
  const [selectedInsurance, setSelectedInsurance] = useState<string | null>(
    null
  );
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>("card");
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch booked dates for this car
  const { data: bookedDates = [] } = useCarBookedDates(carId);

  const { data: insuranceOptions = [] } = useInsuranceOptions();

  const { data: availabilityData } = useCheckCarAvailability(
    carId,
    startDate,
    endDate
  );

  const calculateDays = () => {
    if (!startDate || !endDate) return 0;
    const diff = endDate.getTime() - startDate.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  };

  const calculateTotalPrice = () => {
    const days = calculateDays();
    if (days <= 0) return 0;

    let basePrice = 0;

    if (durationType === "day") {
      basePrice = days * priceDay;
    } else if (durationType === "week") {
      const weeks = Math.ceil(days / 7);
      basePrice = weeks * priceWeek;
    } else if (durationType === "month") {
      const months = Math.ceil(days / 30);
      basePrice = months * priceMonth;
    }

    // Add insurance if selected
    const selectedInsuranceOption = insuranceOptions.find(
      (option) => option.id === selectedInsurance
    );

    const insurancePrice = selectedInsuranceOption
      ? selectedInsuranceOption.price_day * days
      : 0;

    return basePrice + insurancePrice;
  };

  const handleBooking = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to book a car",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Dates required",
        description: "Please select pick-up and drop-off dates",
        variant: "destructive",
      });
      return;
    }

    if (startDate >= endDate) {
      toast({
        title: "Invalid dates",
        description: "Drop-off date must be after pick-up date",
        variant: "destructive",
      });
      return;
    }

    if (!availabilityData?.available) {
      toast({
        title: "Car unavailable",
        description: "This car is not available for the selected dates",
        variant: "destructive",
      });
      return;
    }

    try {
      // Create the booking record first
      const { data: booking, error } = await supabase
        .from("car_bookings")
        .insert({
          car_id: carId,
          user_id: user.id,
          start_date: startDate.toISOString().split("T")[0],
          end_date: endDate.toISOString().split("T")[0],
          duration_type: durationType,
          total_price: calculateTotalPrice(),
        })
        .select()
        .single();

      if (error) throw error;

      // Show payment dialog
      setShowPaymentDialog(true);
      setSelectedBooking(booking);
    } catch (error: any) {
      toast({
        title: "Booking failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleStripePayment = async () => {
    if (!selectedBooking || !user?.email) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke(
        "process-stripe-payment",
        {
          body: {
            carBookingId: selectedBooking.id,
            amount: calculateTotalPrice(),
            returnUrl: window.location.origin + "/car-booking-success",
            userEmail: user.email,
          },
        }
      );

      if (error) throw error;

      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      } else {
        throw new Error("No session URL returned");
      }
    } catch (error: any) {
      console.error("Payment error:", error);
      toast({
        title: "Payment failed",
        description: error.message,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const handleWavePayment = async () => {
    if (!selectedBooking || !user?.email) return;

    setIsLoading(true);
    try {
      // Use supabase.functions.invoke to call the process-wave-payment edge function (cloud)
      const { data: result, error } = await supabase.functions.invoke(
        "process-wave-payment",
        {
          body: {
            bookingId: selectedBooking.id,
            bookingType: "car",
            amount: calculateTotalPrice(),
            returnUrl: window.location.origin,
            userEmail: user.email,
            userMobile: user.phone || "",
          },
        }
      );

      if (error) {
        toast({
          title: "Wave payment failed",
          description: error.message || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      } else if (result?.success && result?.checkoutUrl) {
        window.location.href = result.checkoutUrl;
      } else {
        toast({
          title: "Wave payment failed",
          description: result?.error || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      const err = error as Error;
      console.error("Wave payment error:", err);
      toast({
        title: "Payment failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg rounded-2xl border-0 p-0 bg-white md:sticky md:top-24">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-2xl p-6 border-b">
        <CardTitle className="flex items-center gap-2 text-xl font-bold">
          <Calendar className="h-5 w-5 text-blue-500" /> Booking Details
        </CardTitle>
        <CardDescription className="text-gray-600 mt-1">
          Select your rental period and options
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-400" /> Pick-up & Drop-off
            Dates
          </label>
          <DateRangePicker
            checkInDate={startDate}
            checkOutDate={endDate}
            onCheckInChange={setStartDate}
            onCheckOutChange={setEndDate}
            disabledDates={bookedDates}
            checkInLabel="Pick-up Date"
            checkOutLabel="Drop-off Date"
          />
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-400" /> Rental Duration Type
          </label>
          <Select
            value={durationType}
            onValueChange={(value) => setDurationType(value as DurationType)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select duration type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily (${priceDay}/day)</SelectItem>
              <SelectItem value="week">Weekly (${priceWeek}/week)</SelectItem>
              <SelectItem value="month">
                Monthly (${priceMonth}/month)
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <ShieldCheck className="h-4 w-4 text-green-500" /> Insurance Option
          </label>
          <Select
            value={selectedInsurance || undefined}
            onValueChange={setSelectedInsurance}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select insurance option" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="no-insurance">No Insurance</SelectItem>
              {insuranceOptions.map((option: InsuranceOption) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name} (${option.price_day}/day)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {startDate && endDate && (
          <div className="mt-2">
            <div
              className={`text-sm font-medium flex items-center gap-2 ${
                availabilityData?.available ? "text-green-600" : "text-red-500"
              }`}
            >
              {availabilityData?.available ? (
                <span>✅ Car is available for the selected dates</span>
              ) : (
                <span>❌ Car is not available for the selected dates</span>
              )}
            </div>
          </div>
        )}

        <div className="border-t pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Days:</span>
            <span>{calculateDays()} days</span>
          </div>
          {selectedInsurance && (
            <div className="flex justify-between text-sm">
              <span>Insurance:</span>
              <span>
                $
                {(insuranceOptions.find(
                  (option) => option.id === selectedInsurance
                )?.price_day || 0) * calculateDays()}
              </span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span>${calculateTotalPrice()}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-6 pt-0">
        <div className="w-full md:static fixed left-0 bottom-0 bg-white md:bg-transparent p-4 md:p-0 z-20 border-t md:border-0">
          <Button
            className="w-full py-3 text-base font-semibold rounded-lg shadow-md"
            onClick={handleBooking}
            disabled={
              !startDate ||
              !endDate ||
              !availabilityData?.available ||
              isLoading
            }
          >
            {isLoading ? "Processing..." : "Book Now"}
          </Button>
        </div>
      </CardFooter>

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md rounded-2xl p-0 overflow-hidden">
          <DialogHeader className="bg-blue-50 p-6 border-b">
            <DialogTitle className="text-lg font-bold">
              Select Payment Method
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              Choose how you'd like to pay for your car rental
            </DialogDescription>
          </DialogHeader>
          <div className="p-6">
            <Tabs defaultValue="card" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger
                  value="card"
                  onClick={() => setSelectedPaymentMethod("card")}
                  className="flex items-center gap-2"
                >
                  <CreditCard className="h-4 w-4" /> Credit Card
                </TabsTrigger>
                <TabsTrigger
                  value="wave"
                  onClick={() => setSelectedPaymentMethod("wave")}
                  className="flex items-center gap-2"
                >
                  <Wallet className="h-4 w-4" /> Wave
                </TabsTrigger>
              </TabsList>

              <TabsContent value="card" className="mt-2">
                <div className="flex items-center p-4 border rounded-md mb-4 gap-2">
                  <CreditCard className="h-6 w-6 text-blue-500" />
                  <div>
                    <p className="font-medium">Pay with card</p>
                    <p className="text-sm text-muted-foreground">
                      Secure payment via Stripe
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleStripePayment}
                  disabled={isLoading}
                  className="w-full py-3 text-base font-semibold rounded-lg"
                >
                  {isLoading ? "Processing..." : "Continue to Payment"}
                </Button>
              </TabsContent>

              <TabsContent value="wave" className="mt-2">
                <div className="flex items-center p-4 border rounded-md mb-4 gap-2">
                  <Wallet className="h-6 w-6 text-green-500" />
                  <div>
                    <p className="font-medium">Pay with Wave</p>
                    <p className="text-sm text-muted-foreground">
                      Mobile money payment
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleWavePayment}
                  disabled={isLoading}
                  className="w-full py-3 text-base font-semibold rounded-lg"
                >
                  {isLoading ? "Processing..." : "Continue with Wave"}
                </Button>
              </TabsContent>
            </Tabs>
          </div>
          <DialogFooter className="flex justify-between bg-gray-50 p-4 border-t">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <div>
              <p className="text-right font-semibold">
                Total: ${calculateTotalPrice()}
              </p>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default CarBookingWidget;
