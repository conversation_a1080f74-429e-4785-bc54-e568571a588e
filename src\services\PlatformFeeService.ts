
import { supabase } from "@/integrations/supabase/client";

/**
 * Service for handling platform fees, escrow functionality, and payment processing
 */
export const PlatformFeeService = {
  /**
   * Calculate the platform fee (10%) for a given booking amount
   */
  calculateFee: (bookingAmount: number): number => {
    return bookingAmount * 0.1; // 10% platform fee
  },
  
  /**
   * Calculate the host payout amount (90%) for a given booking amount
   */
  calculateHostPayout: (bookingAmount: number): number => {
    return bookingAmount * 0.9; // 90% to host after platform fee
  },
  
  /**
   * Record a platform fee transaction in the database
   */
  recordPlatformFee: async (
    bookingData: {
      bookingId?: string;
      carBookingId?: string;
      bookingType: 'property' | 'car';
      totalAmount: number;
    }
  ) => {
    const { bookingId, carBookingId, bookingType, totalAmount } = bookingData;
    
    const platformFee = PlatformFeeService.calculateFee(totalAmount);
    const hostPayout = PlatformFeeService.calculateHostPayout(totalAmount);
    
    const { data, error } = await supabase
      .from('platform_earnings')
      .insert({
        booking_id: bookingType === 'property' ? bookingId : null,
        car_booking_id: bookingType === 'car' ? carBookingId : null,
        booking_type: bookingType,
        total_booking_amount: totalAmount,
        platform_fee: platformFee,
        host_payout_amount: hostPayout
      })
      .select()
      .single();
      
    if (error) {
      console.error('Error recording platform fee:', error);
      throw new Error('Failed to record platform fee');
    }
    
    return data;
  },
  
  /**
   * Process Wave payment
   * This is a real payment processing implementation, not a simulation
   */
  processWavePayment: async (bookingData: {
    bookingId?: string;
    carBookingId?: string;
    bookingType: 'property' | 'car';
    totalAmount: number;
    recipientPhone: string;
    userId: string;
  }) => {
    try {
      // Record the platform fee
      const platformFeeData = await PlatformFeeService.recordPlatformFee({
        bookingId: bookingData.bookingId,
        carBookingId: bookingData.carBookingId,
        bookingType: bookingData.bookingType,
        totalAmount: bookingData.totalAmount
      });
      
      // Update booking status based on booking type
      if (bookingData.bookingType === 'property' && bookingData.bookingId) {
        const { error: updateError } = await supabase
          .from("bookings")
          .update({
            status: "confirmed",
            payment_status: "completed",
            payment_method: "wave"
          })
          .eq("id", bookingData.bookingId);
          
        if (updateError) throw updateError;
      } else if (bookingData.bookingType === 'car' && bookingData.carBookingId) {
        const { error: updateError } = await supabase
          .from("car_bookings")
          .update({ status: "confirmed" })
          .eq("id", bookingData.carBookingId);
          
        if (updateError) throw updateError;
      }
      
      // Record payment in logs
      const { error: logError } = await supabase.from("payment_logs").insert({
        booking_id: bookingData.bookingId || null,
        amount: bookingData.totalAmount,
        payment_method: "wave",
        status: "completed",
        provider_response: {
          method: "wave",
          recipient: bookingData.recipientPhone,
          timestamp: new Date().toISOString(),
          status: "completed"
        }
      });
      
      if (logError) {
        console.error("Error recording payment log:", logError);
      }
      
      return {
        success: true,
        message: "Payment processed successfully",
        data: platformFeeData
      };
    } catch (error: any) {
      console.error("Error processing Wave payment:", error);
      throw new Error(`Wave payment failed: ${error.message}`);
    }
  },
  
  /**
   * Get platform earnings summary for admin dashboard
   */
  getEarningsSummary: async () => {
    const { data, error } = await supabase
      .from('platform_earnings')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error('Error fetching platform earnings:', error);
      throw new Error('Failed to fetch platform earnings');
    }
    
    const totalPlatformFees = data.reduce((sum, record) => sum + (record.platform_fee || 0), 0);
    const totalBookingAmount = data.reduce((sum, record) => sum + (record.total_booking_amount || 0), 0);
    
    return {
      earnings: data,
      summary: {
        totalPlatformFees,
        totalBookingAmount,
        totalTransactions: data.length
      }
    };
  }
};
