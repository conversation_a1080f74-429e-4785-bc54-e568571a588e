
import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter } from "lucide-react";
import { Database } from "@/integrations/supabase/types";

type CarType = Database["public"]["Enums"]["car_type"];

interface CarFiltersProps {
  onFilterChange: (filters: {
    type?: CarType;
    location?: string;
    minPrice?: number;
    maxPrice?: number;
    seats?: number;
  }) => void;
}

const CarFilters = ({ onFilterChange }: CarFiltersProps) => {
  const [location, setLocation] = useState("");
  const [carType, setCarType] = useState<CarType | undefined>(undefined);
  const [priceRange, setPriceRange] = useState([0, 500]);
  const [seats, setSeats] = useState<number | undefined>(undefined);

  const handleApplyFilters = () => {
    onFilterChange({
      location: location || undefined,
      type: carType,
      minPrice: priceRange[0],
      maxPrice: priceRange[1],
      seats: seats,
    });
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          <Filter className="h-4 w-4" />
          <span>Filter</span>
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Cars</SheetTitle>
        </SheetHeader>
        
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              placeholder="Search by location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="car-type">Car Type</Label>
            <Select
              onValueChange={(val) => setCarType(val as CarType)}
              value={carType}
            >
              <SelectTrigger id="car-type">
                <SelectValue placeholder="Select car type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sedan">Sedan</SelectItem>
                <SelectItem value="suv">SUV</SelectItem>
                <SelectItem value="luxury">Luxury</SelectItem>
                <SelectItem value="compact">Compact</SelectItem>
                <SelectItem value="convertible">Convertible</SelectItem>
                <SelectItem value="van">Van</SelectItem>
                <SelectItem value="truck">Truck</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>Price Range (daily)</Label>
              <span className="text-sm">
                ${priceRange[0]} - ${priceRange[1]}
              </span>
            </div>
            <Slider
              defaultValue={[0, 500]}
              max={500}
              step={10}
              value={priceRange}
              onValueChange={setPriceRange}
              className="my-4"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="seats">Number of Seats</Label>
            <Select
              onValueChange={(val) => setSeats(parseInt(val))}
              value={seats?.toString()}
            >
              <SelectTrigger id="seats">
                <SelectValue placeholder="Any seats" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2">2 seats</SelectItem>
                <SelectItem value="4">4 seats</SelectItem>
                <SelectItem value="5">5 seats</SelectItem>
                <SelectItem value="7">7+ seats</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button onClick={handleApplyFilters} className="mt-4">
            Apply Filters
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default CarFilters;
