
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/admin/charts";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";

interface BookingAnalytics {
  month: string;
  total_bookings: number;
  total_revenue: number;
  confirmed_bookings: number;
  cancelled_bookings: number;
}

interface PopularListing {
  id: string;
  booking_count: number;
  avg_rating: number;
  total_revenue: number;
  title: string;
  location?: string;
  make?: string;
  model?: string;
}

const AdminAnalytics = () => {
  const [propertyAnalytics, setPropertyAnalytics] = useState<BookingAnalytics[]>([]);
  const [carAnalytics, setCarAnalytics] = useState<BookingAnalytics[]>([]);
  const [popularProperties, setPopularProperties] = useState<PopularListing[]>([]);
  const [popularCars, setPopularCars] = useState<PopularListing[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Fetch property booking analytics
      const { data: propertyData, error: propertyError } = await supabase
        .from('booking_analytics')
        .select('*')
        .order('month', { ascending: true });
      
      if (propertyError) throw propertyError;
      
      // Fetch car booking analytics
      const { data: carData, error: carError } = await supabase
        .from('car_booking_analytics')
        .select('*')
        .order('month', { ascending: true });
      
      if (carError) throw carError;
      
      // Fetch popular properties
      const { data: popularPropertiesData, error: propError } = await supabase
        .from('popular_properties')
        .select('*')
        .order('booking_count', { ascending: false })
        .limit(10);
      
      if (propError) throw propError;
      
      // Fetch popular cars
      const { data: popularCarsData, error: carListingError } = await supabase
        .from('popular_cars')
        .select('*')
        .order('booking_count', { ascending: false })
        .limit(10);
      
      if (carListingError) throw carListingError;
      
      setPropertyAnalytics(propertyData || []);
      setCarAnalytics(carData || []);
      setPopularProperties(popularPropertiesData || []);
      setPopularCars(popularCarsData || []);
    } catch (error: any) {
      toast.error("Failed to fetch analytics data: " + error.message);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const formatMonthLabel = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  // Chart data preparation - Monthly Bookings
  const bookingsChartData = {
    labels: propertyAnalytics.map(item => formatMonthLabel(item.month)),
    datasets: [
      {
        label: 'Property Bookings',
        data: propertyAnalytics.map(item => item.total_bookings),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
      },
      {
        label: 'Car Rentals',
        data: carAnalytics.map(item => item.total_bookings),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      }
    ]
  };

  // Chart data preparation - Monthly Revenue
  const revenueChartData = {
    labels: propertyAnalytics.map(item => formatMonthLabel(item.month)),
    datasets: [
      {
        label: 'Property Revenue',
        data: propertyAnalytics.map(item => item.total_revenue),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
      },
      {
        label: 'Car Rental Revenue',
        data: carAnalytics.map(item => item.total_revenue),
        borderColor: 'rgb(153, 102, 255)',
        backgroundColor: 'rgba(153, 102, 255, 0.5)',
      }
    ]
  };

  // Chart data preparation - Booking Status
  const propertyConfirmed = propertyAnalytics.reduce((sum, item) => sum + item.confirmed_bookings, 0);
  const propertyCancelled = propertyAnalytics.reduce((sum, item) => sum + item.cancelled_bookings, 0);
  const carConfirmed = carAnalytics.reduce((sum, item) => sum + item.confirmed_bookings, 0);
  const carCancelled = carAnalytics.reduce((sum, item) => sum + item.cancelled_bookings, 0);

  const bookingStatusData = {
    labels: ['Confirmed', 'Cancelled'],
    datasets: [
      {
        label: 'Property Bookings',
        data: [propertyConfirmed, propertyCancelled],
        backgroundColor: ['rgba(75, 192, 192, 0.5)', 'rgba(255, 99, 132, 0.5)'],
      }
    ]
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Platform Analytics</h1>
      
      {/* Monthly statistics charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Bookings</CardTitle>
          </CardHeader>
          <CardContent>
            <LineChart
              data={bookingsChartData}
              width={400}
              height={300}
              options={{
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }}
            />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Monthly Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <BarChart
              data={revenueChartData}
              width={400}
              height={300}
              options={{
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }}
            />
          </CardContent>
        </Card>
      </div>
      
      {/* Booking status and popular properties */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Booking Status</CardTitle>
          </CardHeader>
          <CardContent>
            <PieChart
              data={bookingStatusData}
              width={300}
              height={300}
              options={{
                maintainAspectRatio: false,
              }}
            />
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Top 5 Properties by Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Bookings</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Revenue</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {popularProperties.slice(0, 5).map((property) => (
                  <TableRow key={property.id}>
                    <TableCell className="font-medium">{property.title}</TableCell>
                    <TableCell>{property.location}</TableCell>
                    <TableCell>{property.booking_count}</TableCell>
                    <TableCell>{property.avg_rating ? property.avg_rating.toFixed(1) : 'N/A'}</TableCell>
                    <TableCell>${property.total_revenue?.toFixed(2) || 0}</TableCell>
                  </TableRow>
                ))}
                {popularProperties.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center">No data available</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
      
      {/* Popular Cars */}
      <Card>
        <CardHeader>
          <CardTitle>Top 5 Cars by Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Car</TableHead>
                <TableHead>Make/Model</TableHead>
                <TableHead>Bookings</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Revenue</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {popularCars.slice(0, 5).map((car) => (
                <TableRow key={car.id}>
                  <TableCell className="font-medium">{car.title}</TableCell>
                  <TableCell>{car.make} {car.model}</TableCell>
                  <TableCell>{car.booking_count}</TableCell>
                  <TableCell>{car.avg_rating ? car.avg_rating.toFixed(1) : 'N/A'}</TableCell>
                  <TableCell>${car.total_revenue?.toFixed(2) || 0}</TableCell>
                </TableRow>
              ))}
              {popularCars.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">No data available</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAnalytics;
