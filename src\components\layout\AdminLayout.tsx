
import { useState, useEffect } from "react";
import { useNavi<PERSON>, Link, Outlet } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { 
  LayoutDashboard, 
  Users, 
  Home, 
  FileText, 
  CreditCard, 
  BarChart, 
  Settings, 
  LogOut 
} from "lucide-react";

const AdminLayout = () => {
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAdminStatus = async () => {
      setLoading(true);
      
      // Check if user is logged in
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        navigate("/admin/auth");
        return;
      }
      
      // Check if user has admin role
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();
      
      if (profileError || profileData?.role !== 'admin') {
        await supabase.auth.signOut();
        navigate("/admin/auth");
        return;
      }
      
      setIsAdmin(true);
      setLoading(false);
    };
    
    checkAdminStatus();
  }, [navigate]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate("/admin/auth");
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Admin Sidebar */}
      <div className="bg-gray-900 text-white w-full md:w-64 flex-shrink-0">
        <div className="p-4 border-b border-gray-800">
          <h2 className="text-xl font-bold">Admin Dashboard</h2>
        </div>
        <nav className="p-4">
          <ul className="space-y-2">
            <li>
              <Link 
                to="/admin/dashboard" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <LayoutDashboard size={18} />
                <span>Dashboard</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/users" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <Users size={18} />
                <span>Users</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/listings" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <Home size={18} />
                <span>Listings</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/bookings" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <FileText size={18} />
                <span>Bookings</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/payments" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <CreditCard size={18} />
                <span>Payments</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/analytics" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <BarChart size={18} />
                <span>Analytics</span>
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/settings" 
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-800 transition-colors"
              >
                <Settings size={18} />
                <span>Settings</span>
              </Link>
            </li>
            <li className="pt-6">
              <Button 
                variant="ghost" 
                className="flex w-full items-center justify-start gap-2 p-2 text-red-400 hover:text-red-300 hover:bg-gray-800"
                onClick={handleSignOut}
              >
                <LogOut size={18} />
                <span>Sign Out</span>
              </Button>
            </li>
          </ul>
        </nav>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow">
        <Outlet />
      </div>
    </div>
  );
};

export default AdminLayout;
