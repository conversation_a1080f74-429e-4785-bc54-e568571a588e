
-- Create user_preferences table for email notification settings
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  booking_confirmations <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT true,
  status_updates BO<PERSON>EAN NOT NULL DEFAULT true,
  reminders B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Add RLS policies for user_preferences table
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Users can read their own preferences
CREATE POLICY user_preferences_select_policy ON public.user_preferences 
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own preferences
CREATE POLICY user_preferences_update_policy ON public.user_preferences 
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can insert their own preferences
CREATE POLICY user_preferences_insert_policy ON public.user_preferences 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add update_updated_at trigger to user_preferences
CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON public.user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
