
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const MAILGUN_API_KEY = Deno.env.get("MAILGUN_API_KEY") || "";
const MAILGUN_DOMAIN = "gescostay.com"; // Updated domain

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface EmailPayload {
  bookingId: string;
  type?: string; // "confirmation" | "reminder" | "status_update"
  status?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const payload: EmailPayload = await req.json();
    const { bookingId, type = "confirmation", status } = payload;

    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { 
      auth: { persistSession: false } 
    });

    // Get booking details
    const { data: booking, error: bookingError } = await supabaseAdmin
      .from("bookings")
      .select(`
        *, 
        properties(*),
        profiles:user_id(*)
      `)
      .eq("id", bookingId)
      .single();

    if (bookingError || !booking) {
      throw new Error("Booking not found");
    }

    // Get property owner details
    const { data: owner, error: ownerError } = await supabaseAdmin
      .from("profiles")
      .select("*")
      .eq("id", booking.properties.owner_id)
      .single();

    if (ownerError) {
      console.error("Could not fetch owner details:", ownerError);
    }

    // Get user preferences for email notifications
    const { data: guestPrefs } = await supabaseAdmin
      .from("user_preferences")
      .select("*")
      .eq("user_id", booking.user_id)
      .maybeSingle();
    
    const { data: ownerPrefs } = await supabaseAdmin
      .from("user_preferences")
      .select("*")
      .eq("user_id", booking.properties.owner_id)
      .maybeSingle();

    // Check if user wants to receive this type of email
    const shouldSendToGuest = !guestPrefs || 
      (type === "confirmation" && guestPrefs.booking_confirmations) || 
      (type === "status_update" && guestPrefs.status_updates) || 
      (type === "reminder" && guestPrefs.reminders);
    
    const shouldSendToOwner = !ownerPrefs || 
      (type === "confirmation" && ownerPrefs.booking_confirmations) || 
      (type === "status_update" && ownerPrefs.status_updates) || 
      (type === "reminder" && ownerPrefs.reminders);

    // Format dates
    const checkIn = new Date(booking.check_in).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const checkOut = new Date(booking.check_out).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Email content based on type
    let guestSubject = "Booking Confirmation";
    let ownerSubject = "New Booking Notification";
    
    if (type === "reminder") {
      guestSubject = "Upcoming Booking Reminder";
      ownerSubject = "Upcoming Guest Arrival Reminder";
    } else if (type === "status_update" && status) {
      guestSubject = `Booking Status: ${status.charAt(0).toUpperCase() + status.slice(1)}`;
      ownerSubject = `Booking Status Update: ${status.charAt(0).toUpperCase() + status.slice(1)}`;
    }

    // Prepare email to guest
    let guestEmailContent = "";
    if (type === "confirmation") {
      guestEmailContent = `
        <h1>Booking Confirmation</h1>
        <p>Dear ${booking.profiles.first_name || 'Guest'},</p>
        <p>Your booking has been confirmed and paid for. Here are the details:</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
          <li><strong>Total Amount:</strong> $${booking.total_price}</li>
        </ul>
        <p>We hope you enjoy your stay!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil((checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      guestEmailContent = `
        <h1>Upcoming Booking Reminder</h1>
        <p>Dear ${booking.profiles.first_name || 'Guest'},</p>
        <p>This is a friendly reminder that your stay at ${booking.properties.title} begins in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>We look forward to hosting you!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      guestEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${booking.profiles.first_name || 'Guest'},</p>
        <p>The status of your booking at ${booking.properties.title} has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>If you have any questions about this update, please contact us.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    // Prepare email to property owner
    let ownerEmailContent = "";
    if (type === "confirmation") {
      ownerEmailContent = `
        <h1>New Booking Notification</h1>
        <p>Dear ${owner?.first_name || 'Owner'},</p>
        <p>You have a new confirmed booking for your property. Here are the details:</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${booking.profiles.last_name}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
          <li><strong>Total Amount:</strong> $${booking.total_price}</li>
        </ul>
        <p>Please prepare for your guest's arrival.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil((checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      ownerEmailContent = `
        <h1>Upcoming Guest Arrival Reminder</h1>
        <p>Dear ${owner?.first_name || 'Owner'},</p>
        <p>This is a friendly reminder that your guest will be arriving at ${booking.properties.title} in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${booking.profiles.last_name}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Please ensure everything is ready for their arrival.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      ownerEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${owner?.first_name || 'Owner'},</p>
        <p>The status of a booking for your property ${booking.properties.title} has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${booking.profiles.last_name}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    const emailPromises = [];

    // Send email to guest if they want to receive this type of email
    if (shouldSendToGuest && booking.profiles.email) {
      const guestEmailPromise = fetch(
        `https://api.mailgun.net/v3/${MAILGUN_DOMAIN}/messages`,
        {
          method: "POST",
          headers: {
            "Authorization": `Basic ${btoa(`api:${MAILGUN_API_KEY}`)}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams({
            from: `GescosStay <bookings@${MAILGUN_DOMAIN}>`,
            to: booking.profiles.email,
            subject: guestSubject,
            html: guestEmailContent
          }).toString()
        }
      );
      emailPromises.push(guestEmailPromise);
    }

    // Send email to property owner if they want to receive this type of email
    if (shouldSendToOwner && owner?.email) {
      const ownerEmailPromise = fetch(
        `https://api.mailgun.net/v3/${MAILGUN_DOMAIN}/messages`,
        {
          method: "POST",
          headers: {
            "Authorization": `Basic ${btoa(`api:${MAILGUN_API_KEY}`)}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams({
            from: `GescosStay <bookings@${MAILGUN_DOMAIN}>`,
            to: owner.email,
            subject: ownerSubject,
            html: ownerEmailContent
          }).toString()
        }
      );
      emailPromises.push(ownerEmailPromise);
    }

    // Wait for all emails to be sent
    await Promise.all(emailPromises);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Email notifications sent successfully" 
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error sending email notifications:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
