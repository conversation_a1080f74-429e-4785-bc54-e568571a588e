
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { CarListing } from "./useCarListings";

export const useCarDetails = (id: string | undefined) => {
  return useQuery({
    queryKey: ["car", id],
    queryFn: async () => {
      if (!id) throw new Error("Car ID is required");

      const { data, error } = await supabase
        .from("cars")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;

      return data as CarListing;
    },
    enabled: !!id,
  });
};
