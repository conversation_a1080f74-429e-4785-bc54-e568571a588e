
import { Check } from "lucide-react";

const AMENITIES = [
  "Wi-Fi",
  "Free parking",
  "Kitchen",
  "Air conditioning",
  "Heating",
  "TV",
  "Washer",
  "Dryer"
];

const PropertyAmenities = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4">What this place offers</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {AMENITIES.map((amenity, index) => (
          <div key={index} className="flex items-center">
            <Check className="w-5 h-5 mr-2 text-accent" />
            <span>{amenity}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PropertyAmenities;
