import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import ListingsPage from "@/pages/listings/ListingsPage";
import PropertyDetail from "@/pages/listings/PropertyDetail";
import AuthPage from "@/pages/auth/AuthPage";
import CreateListing from "@/pages/listings/CreateListing";
import CarsListingPage from "@/pages/cars/CarsListingPage";
import CarDetailPage from "@/pages/cars/CarDetailPage";
import CreateCarListing from "@/pages/cars/CreateCarListing";
import PaymentSuccess from "@/components/payment/PaymentSuccess";
import BookingsPage from "@/pages/bookings/BookingsPage";
import ProfilePage from "@/pages/profile/ProfilePage";
import { Database } from "@/integrations/supabase/types";

// Admin pages
import AdminAuth from "@/pages/admin/AdminAuth";
import AdminLayout from "@/components/layout/AdminLayout";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import AdminUsers from "@/pages/admin/AdminUsers";
import AdminListings from "@/pages/admin/AdminListings";
import AdminBookings from "@/pages/admin/AdminBookings";
import AdminPayments from "@/pages/admin/AdminPayments";
import AdminAnalytics from "@/pages/admin/AdminAnalytics";
import AdminSettings from "@/pages/admin/AdminSettings";

// Host pages
import HostLayout from "@/components/layout/HostLayout";
import HostDashboard from "@/pages/host/HostDashboard";
import HostListings from "@/pages/host/HostListings";
import HostEarnings from "@/pages/host/HostEarnings";
import HostReservations from "@/pages/host/HostReservations";
import HostMessages from "@/pages/host/HostMessages";
import HostSettings from "@/pages/host/HostSettings";

type UserRole = Database["public"]["Enums"]["user_role"];

const AppRoutes = () => {
  const { user, loading } = useAuth();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [checkingRole, setCheckingRole] = useState(true);

  useEffect(() => {
    const checkUserRole = async () => {
      if (!user) {
        setUserRole(null);
        setUserRoles([]);
        setCheckingRole(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from("profiles")
          .select("role, roles")
          .eq("id", user.id)
          .single();

        if (error) throw error;
        setUserRole(data.role);
        setUserRoles(data.roles || []);
      } catch (error) {
        console.error("Error checking user role:", error);
        setUserRole(null);
        setUserRoles([]);
      } finally {
        setCheckingRole(false);
      }
    };

    if (!loading) {
      checkUserRole();
    }
  }, [user, loading]);

  // Check if user is admin
  const isAdmin = userRole === "admin" || userRoles.includes("admin");
  const isHost = userRole === "host" || userRoles.includes("host");
  // Admins can also access host features, but they have their own dedicated interface

  if (loading || checkingRole) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Main App Routes */}
      <Route path="/" element={<Index />} />
      <Route path="/listings" element={<ListingsPage />} />
      <Route
        path="/listings/create"
        element={user ? <CreateListing /> : <Navigate to="/auth" replace />}
      />
      <Route path="/listings/:id" element={<PropertyDetail />} />
      <Route path="/payment-success" element={<PaymentSuccess />} />
      <Route path="/payment-cancelled" element={<PropertyDetail />} />

      {/* Car Rental Routes */}
      <Route path="/cars" element={<CarsListingPage />} />
      <Route path="/cars/:id" element={<CarDetailPage />} />
      <Route
        path="/cars/create"
        element={user ? <CreateCarListing /> : <Navigate to="/auth" replace />}
      />

      {/* Booking Management Routes */}
      <Route
        path="/bookings"
        element={user ? <BookingsPage /> : <Navigate to="/auth" replace />}
      />

      {/* Profile Route - for regular users */}
      <Route
        path="/profile"
        element={user ? <ProfilePage /> : <Navigate to="/auth" replace />}
      />

      <Route
        path="/auth"
        element={
          !user ? (
            <AuthPage />
          ) : isHost ? (
            <Navigate to="/host" replace />
          ) : isAdmin ? (
            <Navigate to="/admin/dashboard" replace />
          ) : (
            <Navigate to="/" replace />
          )
        }
      />

      {/* Host Routes */}
      <Route
        path="/host"
        element={
          user ? (
            isHost ? (
              <HostLayout />
            ) : isAdmin ? (
              <Navigate to="/admin/dashboard" replace />
            ) : (
              <Navigate to="/profile" replace />
            )
          ) : (
            <Navigate to="/auth" replace />
          )
        }
      >
        <Route index element={<HostDashboard />} />
        <Route path="listings" element={<HostListings />} />
        <Route path="reservations" element={<HostReservations />} />
        <Route path="earnings" element={<HostEarnings />} />
        <Route path="messages" element={<HostMessages />} />
        <Route path="settings" element={<HostSettings />} />
      </Route>

      {/* Admin Routes */}
      <Route path="/admin/auth" element={<AdminAuth />} />
      <Route
        path="/admin"
        element={
          user && isAdmin ? (
            <Navigate to="/admin/dashboard" replace />
          ) : (
            <Navigate to="/admin/auth" replace />
          )
        }
      />
      <Route
        path="/admin"
        element={
          user && isAdmin ? (
            <AdminLayout />
          ) : (
            <Navigate to="/admin/auth" replace />
          )
        }
      >
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users" element={<AdminUsers />} />
        <Route path="listings" element={<AdminListings />} />
        <Route path="bookings" element={<AdminBookings />} />
        <Route path="payments" element={<AdminPayments />} />
        <Route path="analytics" element={<AdminAnalytics />} />
        <Route path="settings" element={<AdminSettings />} />
      </Route>

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
