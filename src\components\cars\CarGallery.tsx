import { useState } from "react";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";

interface CarGalleryProps {
  images: string[];
  title: string;
}

const CarGallery = ({ images, title }: CarGalleryProps) => {
  const [mainImage, setMainImage] = useState(images[0] || "/placeholder.svg");

  return (
    <div className="space-y-4">
      <div className="overflow-hidden rounded-lg border">
        <AspectRatio ratio={16 / 9} className="bg-muted">
          <img
            src={mainImage}
            alt={`${title} - Main view`}
            className="object-cover w-full h-full"
          />
        </AspectRatio>
      </div>

      {/* Thumbnails Row */}
      <div className="flex overflow-x-auto gap-2 pb-1">
        {images.map((image, index) => (
          <Button
            key={index}
            variant="ghost"
            className={`p-0 overflow-hidden rounded-md min-w-[80px] min-h-[80px] w-20 h-20 flex-shrink-0 border border-gray-200 hover:border-accent transition-all duration-150 ${
              image === mainImage ? "ring-2 ring-accent border-accent" : ""
            }`}
            onClick={() => setMainImage(image)}
            aria-label={`Show image ${index + 1}`}
          >
            <AspectRatio ratio={1 / 1} className="bg-muted">
              <img
                src={image}
                alt={`${title} - View ${index + 1}`}
                className="object-cover w-full h-full"
              />
            </AspectRatio>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default CarGallery;
