
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export type Review = {
  id: string;
  property_id: string | null;
  car_id: string | null;
  user_id: string;
  rating: number;
  comment: string | null;
  created_at: string | null;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  };
};

export const useReviews = (propertyId: string | undefined) => {
  return useQuery({
    queryKey: ["reviews", propertyId],
    queryFn: async () => {
      if (!propertyId) return [];

      const { data, error } = await supabase
        .from("reviews")
        .select(`
          *,
          profiles:user_id(first_name, last_name, avatar_url)
        `)
        .eq("property_id", propertyId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Type cast the response to match our Review type
      return (data || []).map(item => {
        return {
          ...item,
          profiles: item.profiles as unknown as Review['profiles']
        } 
      }) as Review[];
    },
    enabled: !!propertyId,
  });
};
