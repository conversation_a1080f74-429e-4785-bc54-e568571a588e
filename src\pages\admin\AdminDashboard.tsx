
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/admin/charts";
import { Users, Home, FileText, CreditCard, Calendar } from "lucide-react";
import NotificationsPanel from "@/components/admin/NotificationsPanel";
import RevenueReport from "@/components/admin/RevenueReport";
import UserActivityChart from "@/components/admin/UserActivityChart";
import EmailPreview from "@/components/admin/EmailPreview";

interface DashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalCars: number;
  totalBookings: number;
  totalRevenue: number;
  recentBookings: any[];
  popularProperties: any[];
  popularCars: any[];
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalProperties: 0,
    totalCars: 0,
    totalBookings: 0,
    totalRevenue: 0,
    recentBookings: [],
    popularProperties: [],
    popularCars: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch counts
        const [usersRes, propertiesRes, carsRes, bookingsRes, carBookingsRes] = await Promise.all([
          supabase.from('profiles').select('id', { count: 'exact', head: true }),
          supabase.from('properties').select('id', { count: 'exact', head: true }),
          supabase.from('cars').select('id', { count: 'exact', head: true }),
          supabase.from('bookings').select('total_price'),
          supabase.from('car_bookings').select('total_price')
        ]);

        // Fetch popular properties and cars
        const [popularPropertiesRes, popularCarsRes] = await Promise.all([
          supabase.from('popular_properties').select('*').limit(5),
          supabase.from('popular_cars').select('*').limit(5)
        ]);

        // Calculate total revenue
        const propertyBookingsTotal = bookingsRes.data?.reduce((sum, booking) => sum + parseFloat(booking.total_price.toString()), 0) || 0;
        const carBookingsTotal = carBookingsRes.data?.reduce((sum, booking) => sum + parseFloat(booking.total_price.toString()), 0) || 0;

        setStats({
          totalUsers: usersRes.count || 0,
          totalProperties: propertiesRes.count || 0,
          totalCars: carsRes.count || 0,
          totalBookings: (bookingsRes.data?.length || 0) + (carBookingsRes.data?.length || 0),
          totalRevenue: propertyBookingsTotal + carBookingsTotal,
          recentBookings: [], // We'll implement this if needed
          popularProperties: popularPropertiesRes.data || [],
          popularCars: popularCarsRes.data || []
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Sample data for charts
  const monthlyBookingData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Property Bookings',
        data: [30, 40, 45, 50, 49, 60, 70, 91, 125, 130, 121, 135],
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
      },
      {
        label: 'Car Rentals',
        data: [20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 60, 70],
        fill: false,
        borderColor: 'rgb(255, 99, 132)',
      }
    ]
  };

  const revenueData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Revenue',
        data: [3000, 4000, 4500, 5000, 4900, 6000, 7000, 9100, 12500, 13000, 12100, 13500],
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
      }
    ]
  };

  const listingDistributionData = {
    labels: ['Properties', 'Cars'],
    datasets: [
      {
        data: [stats.totalProperties, stats.totalCars],
        backgroundColor: ['rgba(54, 162, 235, 0.5)', 'rgba(255, 99, 132, 0.5)'],
      }
    ]
  };

  // Sample revenue report data
  const revenueReportData = {
    daily: {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [
        {
          label: 'Revenue',
          data: [1200, 950, 1100, 1300, 1600, 2200, 1800],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
        }
      ]
    },
    weekly: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [
        {
          label: 'Revenue',
          data: [4500, 5200, 7100, 8300],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
        }
      ]
    },
    monthly: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      datasets: [
        {
          label: 'Revenue',
          data: [3000, 4000, 4500, 5000, 4900, 6000, 7000, 9100, 12500, 13000, 12100, 13500],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
        }
      ]
    },
    yearly: {
      labels: ['2023', '2024', '2025'],
      datasets: [
        {
          label: 'Revenue',
          data: [75000, 125000, 85000],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
        }
      ]
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="bg-blue-100 p-3 rounded-full mr-4">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Users</p>
              <h3 className="text-2xl font-bold">{stats.totalUsers}</h3>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="bg-green-100 p-3 rounded-full mr-4">
              <Home className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Listings</p>
              <h3 className="text-2xl font-bold">{stats.totalProperties + stats.totalCars}</h3>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="bg-purple-100 p-3 rounded-full mr-4">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Bookings</p>
              <h3 className="text-2xl font-bold">{stats.totalBookings}</h3>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="bg-yellow-100 p-3 rounded-full mr-4">
              <CreditCard className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <h3 className="text-2xl font-bold">${stats.totalRevenue.toFixed(2)}</h3>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <RevenueReport data={revenueReportData} />
        <UserActivityChart />
      </div>
      
      {/* Notifications & Email Templates */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <NotificationsPanel />
        <EmailPreview />
      </div>
      
      {/* Popular Listings & Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Popular Properties</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.popularProperties.length > 0 ? (
              <div className="space-y-4">
                {stats.popularProperties.map((property) => (
                  <div key={property.id} className="flex justify-between items-center border-b pb-2">
                    <div>
                      <p className="font-medium">{property.title}</p>
                      <p className="text-sm text-gray-500">{property.location}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${property.total_revenue?.toFixed(2) || 0}</p>
                      <p className="text-sm text-gray-500">{property.booking_count || 0} bookings</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No popular properties data available</p>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Listing Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <PieChart
              data={listingDistributionData}
              width={300}
              height={300}
              options={{
                maintainAspectRatio: false,
              }}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
