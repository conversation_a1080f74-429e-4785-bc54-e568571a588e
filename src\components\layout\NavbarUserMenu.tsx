import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "./AuthProvider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";

type UserRole = Database["public"]["Enums"]["user_role"];

const NavbarUserMenu = () => {
  const { user, signOut, getUserRole, getUserRoles, becomeHost } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadUserRole();
    }
  }, [user, location.pathname]); // Refresh role when location changes

  const loadUserRole = async () => {
    if (user) {
      const role = await getUserRole();
      const roles = await getUserRoles();

      setUserRole(role);
      setUserRoles(roles);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    navigate("/");
  };

  const handleSwitchRole = async () => {
    try {
      setIsLoading(true);

      // Check if user has host role
      const isAlreadyHost = userRoles.includes("host");

      if (isAlreadyHost) {
        // User is already a host, just navigate to host dashboard
        navigate("/host");
        toast.success("Switched to Host mode");
      } else {
        // User is not a host yet, become a host
        await becomeHost();
        // Refresh the user role after becoming host
        await loadUserRole();
      }
    } catch (error: any) {
      console.error("Error switching role:", error);
      toast.error(`Failed to switch role: ${error.message || "Unknown error"}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = () => {
    if (!user) return "U";
    const name = user.user_metadata?.first_name || user.email || "";
    return name.charAt(0).toUpperCase();
  };

  if (!user) {
    return (
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => navigate("/auth?mode=signin")}>
          Sign In
        </Button>
        <Button onClick={() => navigate("/auth?mode=signup")}>Sign Up</Button>
      </div>
    );
  }

  // Check if user is a host or admin
  const isHost = userRoles.includes("host");
  const isAdmin = userRole === "admin" || userRoles.includes("admin");
  // const isAdmin = userRole === "admin";
  const isOnHostPage = location.pathname.startsWith("/host");

  return (
    <div className="flex items-center gap-3">
      {!isAdmin && !isHost && (
        <Button
          onClick={handleSwitchRole}
          className="text-sm"
          disabled={isLoading}
        >
          {isLoading ? "Processing..." : "Become a Host"}
        </Button>
      )}

      {!isAdmin && isHost && !isOnHostPage && (
        <Button
          onClick={handleSwitchRole}
          className="text-sm"
          disabled={isLoading}
        >
          {isLoading ? "Processing..." : "Switch to Host"}
        </Button>
      )}

      {!isAdmin && isHost && isOnHostPage && (
        <Button
          onClick={() => navigate("/")}
          className="text-sm"
          disabled={isLoading}
        >
          {isLoading ? "Processing..." : "Switch to Guest"}
        </Button>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-10 w-10 rounded-full">
            <Avatar>
              <AvatarImage
                src={user.user_metadata?.avatar_url || ""}
                alt={user.user_metadata?.first_name || "User avatar"}
              />
              <AvatarFallback>{getInitials()}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>My Account</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Common menu items */}
          <DropdownMenuItem onClick={() => navigate("/profile")}>
            Profile
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => navigate("/bookings")}>
            My Bookings
          </DropdownMenuItem>

          {/* Role-specific items */}
          {isHost && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Host Menu</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigate("/host")}>
                Host Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate("/host/listings")}>
                Manage Listings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate("/host/reservations")}>
                Reservations
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate("/host/earnings")}>
                Payments & Earnings
              </DropdownMenuItem>
            </>
          )}

          {/* Admin-specific items */}
          {isAdmin && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Admin</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigate("/admin/dashboard")}>
                Admin Dashboard
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSignOut}>Sign out</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default NavbarUserMenu;
